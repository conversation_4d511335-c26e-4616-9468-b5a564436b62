/*
 * @Date: 2024-11-14 15:17:16
 * @Description: 启动文件
 */

import './core'
import { useEffect } from 'react'
import { Provider } from 'react-redux'
import { store, actions, dispatch, useSelector } from '@/core/store'
import './app.scss'

const App = (props) => {
  const token = useSelector((state) => state.storage.token)
  const r = $.taro.getLaunchOptionsSync()
  const { forceLogin } = r.query || {}

  // useEffect(() => {
  //   dispatch(actions.storage.setItem({ key: 'token', value: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.vaXptD6KmFue2gK-N7YCRJ2WqcZPRDWh_3n24cxg0y8' }))
  //   dispatch(actions.storage.setItem({ key: 'userInfo', value: { userId: 25411218, tenantKey: 'YPZP', userName: '先生', headPortrait: 'https://static-test-public.cdqlkj.cn/r/dd4e/108/pb/p/20241011/49cec3801b00442dbe75e36402d10ba2.jpeg', status: 1, tel: '***********', nameAuditStatus: 0, headPortraitAuditStatus: 0, role: { firstRole: 0, nowRole: 0 }, newMember: false, uuid: '1747287726033380', curCityId: null, entangledUserInfo: null } }))
  // }, [])

  useEffect(() => {
    dispatch(actions.classify.getClassTreeData())
    dispatch(actions.classify.getClassifyConfig())
  }, [token])

  useEffect(() => {
    if (forceLogin && !token) {
      setTimeout(() => {
        $.forceLogin()
      }, 500)
    }
  }, [forceLogin])

  return props.children
}

export default (props) => {
  return (
    <Provider store={store}>
      <App {...props} />
    </Provider>
  )
}
