.scroll {
  height: 100%;
  .item {
    min-height: 96px;
    display: flex;
    align-items: center;
    padding: 27px 24px;
    position: relative;
    padding-right: 56px;
  }
  .item-name {
    display: inline-flex;
  }
  .item-right {
    position: absolute;
    right: 24px;
  }
  .item-single {
    background-color: $primary-color;
    height: 12px;
    width: 12px;
    border-radius: 50%;
  }
  .item-count {
    font-size: 20px;
    color: #FFF;
    background-color: $primary-color;
    padding: 0 8px;
    height: 28px;
    min-width: 28px;
    border-radius: 28px;
    display: inline-flex;
    text-align: center;
    align-items: center;
    justify-content: center;
    font-weight: bold;
  }
  &.list-1 {
    background-color: #f5f6fa;
    .item.checked {
      color: $primary-color;
      font-weight: bold;
      background-color: #FFF;
      &::before {
        content: '';
        position: absolute;
        width: 6px;
        height: 40px;
        left: 0;
        background-color: $primary-color;
        border-radius: 0px 4px 4px 0px;

      }
    }
  }
  &.list-2 {
    .item.checked {
      color: $primary-color;
      font-weight: bold;
      background-color: #FFF;
    }
  }
  &.list-3 {
    @include left-line-b;
    .item.checked {
      color: $primary-color;
      font-weight: bold;
      background-color: #FFF;
    }
  }
}

.bottom {
  padding-bottom: 24px;
}

.bottom-safe {
  @include safe-area(24px);
  /* padding-bottom: calc(24px + constant(safe-area-inset-bottom));
  padding-bottom: calc(24px + env(safe-area-inset-bottom)); */
}
