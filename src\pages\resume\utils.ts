import { store } from '@/core/store'

// 获取默认数据
export const getDefaultData = async () => {
  const params: any = {}
  const { userLocation } = store.getState().storage
  const { longitude, latitude } = userLocation || {}
  if (longitude && latitude) {
    params.longitude = longitude
    params.latitude = latitude
  }
  const [data] = await $.request['POST/resume/v3/publish/showDefault'](params)
  const { exist, hopeAreas = [], occupations = [], publishRuleSwitch } = data || { exist: false }
  return { exist, hopeAreas, occupations, publishRuleSwitch }
}

/**
 * 发布埋点
 * @param type 0--快速发布，1---自动发布
 * @param success  0--失败，1---成功 */
export const publishResumeResultReport = (type, success, params, ext: any = {}) => {
  const { occupations, hopeArea } = params || {}
  const { scene } = $.taro.getLaunchOptionsSync() || {}
  $.report.event('publishResumeResult', {
    published_results_id: success,
    published_work: (occupations || []).map((item) => item.industries.map(ind => `${ind}_${item.occupation}`)).flat().join(','),
    release_city_id: (hopeArea || []).join(','),
    published_type: type,
    level_3_work_click: '',
    level_3_work_exposure: '',
    track_seed: '',
    scene,
    published_source_id: ext?.published_source_id || '',
  })
}
