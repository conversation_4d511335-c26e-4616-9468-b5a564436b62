import qs from 'qs'
import { actions, dispatch } from '@/core/store'

/** 登录之后拉取用户信息 */
export const afterLogin = async (token: string) => {
  try {
    dispatch(actions.resume.fetchGlobalConfig(true))
    /** 保存token */
    dispatch(actions.storage.setItem({ key: 'token', value: token }))
    /** 获取用户信息 */
    const [userInfo, res] = await $.request['POST/account/v1/userBase/getLoginUserInfo']({ token })
    if (userInfo) {
      /** 保存用户信息 */
      dispatch(actions.storage.setItem({ key: 'userInfo', value: userInfo }))
      $.report.config({ user_unique_id: userInfo.userId })
      reportUserLogin(userInfo)
    } else {
      $.msg(res.message)
    }
  } catch (error) {
    console.error(error)
  }
}

/** 隐私协议 */
const agreementPrivacy = 'https://h5hybridprod.yupaowang.com/article-out?id=1601060983336411219'
/** 服务协议 */
const agreementService = 'https://h5hybridprod.yupaowang.com/article-out?id=1643075036946485318'
export const getAgreementUrl = (type: string) => encodeURIComponent((type === 'user' ? agreementService : agreementPrivacy))

/** 去同意协议 */
export const goToAgreement = (e) => {
  e.stopPropagation && e.stopPropagation()
  const { type } = e.target.dataset
  $.router.push('/subpackage/web-view/index', { url: getAgreementUrl(type) })
}

const reportUserLogin = (userInfo) => {
  const { query = {}, scene } = $.taro.getEnterOptionsSync()

  $.report.event('siginIn', {
    sharer_id: '-99999',
    sign: userInfo.newMember ? '新用户' : '老用户',
    track_seed: '',
    scene,
    query: qs.stringify(query),
  })
}
