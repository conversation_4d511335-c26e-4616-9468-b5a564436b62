import { <PERSON>er<PERSON>ie<PERSON>, PickerViewColumn, View } from '@tarojs/components'
import { useEffect, useState } from 'react'
import dayjs from 'dayjs'
import s from './index.module.scss'

type INameViewProps = {
  value: string
  mustObj?:any
  onChanges?: (e: any, type: string) => void
  [key: string]: any
}
const monthsAll = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12']

const NameView = (props: INameViewProps) => {
  const { value, onChanges, mustObj = {} } = props
  const { status, must } = mustObj || {}

  const [values, setValues] = useState<Array<any>>([])
  const [years, setYears] = useState<Array<any>>([])
  const [months, setMonths] = useState<Array<any>>([])
  const [data, setData] = useState<any>({ curmonth: 0, startYear: 0, endYear: 0 })

  useEffect(() => {
    let months = [...monthsAll]
    let nData = { ...data }
    let nYears = [...years]
    if (!$.isArrayVal(nYears)) {
      const { years: hYears, ...other } = onInitYear()
      nYears = [...hYears]
      nData = { ...other }
    }
    const { curmonth, endYear, startYear } = nData || {}
    let values: Array<any> = []
    if (value) {
      // setOValue(value)
      const year = dayjs(value).format('YYYY')
      const month = dayjs(value).format('MM')
      const yearIdx = nYears.findIndex(y => y == year)
      if (curmonth > 0) {
        if (year == endYear) {
          months = months.filter((m) => Number(m) <= curmonth)
        } else if (year == startYear) {
          months = months.filter((m) => Number(m) >= curmonth)
        }
      }
      const monthIdx = months.findIndex(m => Number(m) == Number(month))
      values = [yearIdx, monthIdx]
    } else {
      const defDate = dayjs().subtract(18, 'year')
      const year = defDate.format('YYYY')
      const month = defDate.format('MM')
      const yearIdx = nYears.findIndex(y => y == year)
      const monthIdx = months.findIndex(m => Number(m) == Number(month))
      values = [yearIdx, monthIdx]
      // setOValue(`${nYears[yearIdx]}-${months[monthIdx]}`)
      onChanges && onChanges({ detail: { value: `${nYears[yearIdx]}-${months[monthIdx]}` } }, 'birthday')
    }
    setValues(values)
    setMonths(months)
  }, [])

  const onInitYear = () => {
    const nYears:Array<any> = []
    const startYear = Number(dayjs().subtract(100, 'year').format('YYYY'))
    const endYear = Number(dayjs().subtract(16, 'year').format('YYYY'))
    const curmonth = dayjs().set('year', startYear).format('MM')
    // eslint-disable-next-line no-plusplus
    for (let i:number = startYear; i <= endYear; i++) {
      nYears.push(i)
    }
    setYears(nYears)
    setData({ curmonth, startYear, endYear })
    return { years: nYears, curmonth, startYear, endYear }
  }
  const onPickerChanges = (e) => {
    const { value } = e.detail
    let newMonths:Array<string> = []
    const { startYear, endYear } = data || {}
    const year = years[value[0]]
    const curmonth = dayjs().format('MM')
    if (values[0] != value[0]) {
      if (year == endYear) {
        newMonths = monthsAll.filter((m) => Number(m) <= Number(curmonth))
      } else if (year == startYear) {
        newMonths = monthsAll.filter((m) => Number(m) >= Number(curmonth))
      } else if (months.length != 12) {
        newMonths = [...monthsAll]
      }
      if ($.isArrayVal(newMonths)) {
        const oMonth = months[value[1]]
        value[1] = newMonths.findIndex(m => Number(m) == Number(oMonth))
      } else {
        newMonths = [...monthsAll]
      }
    } else {
      newMonths = [...months]
    }

    setValues(value)
    setMonths(newMonths)
    onChanges && onChanges({ detail: { value: `${years[value[0]]}-${months[value[1]]}` } }, 'birthday')
  }
  return status ? (
    <V className={s.item}>
      <V className={s.label}>
        {must && <T className={s.must}>*</T>}<T>出生年月</T>
      </V>
      <V className={s.dtpView}>
        <PickerView indicatorStyle='height:104rpx !important;' indicatorClass={s.indicatorClass} style="width: 100%; height: 312rpx;" value={values} onChange={onPickerChanges}>
          <PickerViewColumn>
            {
              years.map((item, index) => {
                return <View className={s.colClass} style="height: 104rpx !important;" key={index}>{item}年</View>
              })
            }
          </PickerViewColumn>
          <PickerViewColumn>
            {
              months.map((item, index) => {
                return <View className={s.colClass} style="height: 104rpx !important;" key={index}>{item}月</View>
              })
            }
          </PickerViewColumn>
        </PickerView>
      </V>
    </V>
  ) : null
}

export default NameView
