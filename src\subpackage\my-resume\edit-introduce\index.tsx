import cn from 'classnames'
import { Textarea } from '@tarojs/components'
import { useState } from 'react'
import { useLoad, useReady } from '@tarojs/taro'
import s from './index.module.scss'
import { getState } from '@/core/store'

export default function Index() {
  const [data, setData] = useState({
    maxContent: 500,
    isFocus: false,
    bottomHeight: 0,
    /** 页面传入的内容 */
    contentOld: '',
  })

  /** 输入框输入的内容 */
  const [content, setContent] = useState('')
  /** 输入框默认的内容 */
  const [contentDef, setContentDef] = useState<string>('')

  useLoad(() => {
    const { content } = $.router.data || {}
    setContentDef(content)
    setContent(content)
    setData({
      ...data,
      contentOld: content,
    })
  })

  useReady(async () => {
    await $.wait(300)
    setData({ ...data, isFocus: true })
  })

  const onClear = () => {
    setContent('')

    setContentDef(' ')
    setTimeout(
      () => setContentDef(''),
      35,
    )
  }

  /** 保存编辑的内容 */
  const onSave = async () => {
    const introduce = `${content}`.trim()
    if (!saveBool()) {
      return
    }
    const { resumeUuid } = getState().storage.myResumeDetails
    $.showLoading('保存中...')
    const [, res] = await $.request['POST/resume/v3/perfect/introduce']({
      resumeUuid,
      introduce,
    }).catch((err) => err)
    $.hideLoading()
    if (res.code == 0) {
      $.msg('保存成功', 2000, true).then(() => {
        $.router.event(res)
        $.router.back()
      })
    } else {
      $.msg(res.msg || '保存失败')
    }
  }

  /** 判断输入的内容是否有效 */
  const saveBool = () => {
    if (!content || `${content}`.trim() == '') {
      $.msg('个人优势不能为空')
      return false
    }
    if (data.contentOld === content) {
      $.router.back()
      return false
    }
    if (content.length > data.maxContent) {
      $.msg('已超出最大字数限制')
      return false
    }
    return true
  }

  const onFocus = (e) => {
    const newData = {
      ...data,
      isFocus: true,
      bottomHeight: e.detail.height || 0,
    }
    setData(newData)

    // 页面回到顶部
    /* $.taro.pageScrollTo({
      scrollTop: 0,
      duration: 0,
    }) */
    if ($.taro.getEnv() === 'ALIPAY' && $.isIos()) {
      getKeyHeight(newData)
    }
  }

  const getKeyHeight = (newData, loop = 0) => {
    if (loop >= 10) {
      return
    }
    const keyboardH = $.taro.getStorageSync('keyboardH')
    if (!keyboardH || keyboardH < 10) {
      setTimeout(() => {
        getKeyHeight(newData, loop + 1)
      }, 300)
      return
    }
    setData({
      ...newData,
      bottomHeight: keyboardH,
    })
  }

  const onHeightChange = (e) => {
    setData({
      ...data,
      bottomHeight: e.detail.height || 0,
    })
  }

  /** 收起键盘 */
  const onHideKey = () => {
    setData({
      ...data,
      isFocus: false,
      bottomHeight: 0,
    })
  }

  return (<>
    {$.taro.getEnv() === 'ALIPAY' && <keyboard />}
    <V className={s.body}>
      <V onClick={onHideKey} className={s.head}>
        <V className={s.title}>我的优势</V>
        <V className={s.desc}>丰富我的优势介绍，更能赢得老板青睐</V>
      </V>
      <Textarea
        className={s.textarea}
        style={(data.bottomHeight > 10 && data.isFocus) ? 'height: 25vh' : ''}
        placeholder='等待输入内容'
        placeholderClass='等待输入内容'
        value={contentDef}
        maxlength={-1}
        adjustPosition
        disableDefaultPadding
        showConfirmBar={false}
        focus={data.isFocus}
        onKeyboardHeightChange={onHeightChange}
        onFocus={onFocus}
        onBlur={onHideKey}
        onInput={({ detail }) => setContent(detail.value)}
      >
      </Textarea>
      { !(data.bottomHeight > 10 && data.isFocus)
        && <V className={s['footer-full']}></V>
      }

    </V>
    <V className={s.footer} style={(data.bottomHeight > 10 && data.isFocus) ? `padding-bottom: 0;bottom: ${data.bottomHeight || 0}px` : ''}>
      <V className={s['info-text']}>
        <V onClick={() => onClear()} className={cn(s.clear, content.length < 1 && s.disabled)}>清空内容</V>
        <V className={s['info-num']}>
          <V className={cn((content.length > data.maxContent) ? s['num-err'] : s.num, content.length < 1 && s['num-gray'])}>
            {content.length || 0}
          </V>
          <V className={s['num-gray']}>/{data.maxContent}</V>
          <V className={cn(s.btn, s['btn-mini'], !(data.isFocus && data.bottomHeight > 10) && s.hidden)}
            onClick={() => onSave()}
          >
            确定
          </V>
        </V>
      </V>
      <V className={cn(s.btn, (data.isFocus && data.bottomHeight > 10) && s.hidden)} onClick={onSave}>确定</V>
    </V>
  </>)
}
