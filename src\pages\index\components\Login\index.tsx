/*
 * @Date: 2024-11-28 17:02:29
 * @Description: 登录浮标
 */

import { View as V, Text as T } from '@tarojs/components'
import cn from 'classnames'
import Click from '@/components/Click'
import s from './index.module.scss'
import { useSelector } from '@/core/store'

export default () => {
  const token = useSelector((state) => state.storage.token)

  if (token) {
    return null
  }

  return (
    <>
      <V className={cn(s.height)} />
      <V className={cn(s.body)}>
        <T className={s.text}>登录后可获得更多精准职位匹配</T>
        <Click className={s.btn} onClick={() => $.login()}>立即登录</Click>
      </V>
    </>
  )
}
