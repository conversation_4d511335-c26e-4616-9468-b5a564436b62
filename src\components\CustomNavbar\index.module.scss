.box {
  view, text, button {
    box-sizing: content-box;
  }
}

.customNavbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 10;
  background-color: rgba(255, 255, 255, 1);
}

.navbarLeft,
.navbarRight,
.navbarHome {
  width: 88rpx;
  // height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.navbarTitle {
  flex: 1;
  text-align: center;
  font-size: 32rpx;
  font-weight: bold;
}

.iconfont {
  font-size: 40rpx;
}

.zw {
  width: 100vw;
}
