/**
 * 初始化数据
 * @param isGetTem 是否重新获取模板
 * */

import { store } from '@/core/store'
import { getSalaryText, handleDataList } from '@/core/utils/publish'
import storage from '@/store/storage/storage'
import { getResumeDetails } from '@/utils/helper/resume'

/**
 * 初始化数据
 * @param isGetTem 是否重新获取模板
 * */
// eslint-disable-next-line sonarjs/cognitive-complexity
export async function init(isGetTem = false, occIdArr:Array<any> = [], ext: any = {}, fns: any = {}) {
  const { setData, onPerFectChange } = fns || {}
  const { codeObj, publishFlow, query, saveParams } = ext
  let { positionType, salaryObj, salary, template } = ext
  const { origin, numFull } = query || {}
  if (positionType == 1 && numFull == 0) {
    positionType = 2
  }
  // 发布简历 获取缓存数据
  if (origin == 'add') {
    const pubishData = storage.getItemSync('pubishData') || {} as any
    if (pubishData.positionType) {
      positionType = pubishData.positionType
    }
    if ($.isArrayVal(pubishData.preferenceList)) {
      onPerFectChange && onPerFectChange(pubishData.preferenceList)
    }

    if ($.isObjVal(pubishData.salaryObj)) {
      salaryObj = pubishData.salaryObj || {}
      const { valFormat } = salaryObj || {} as any
      salary = valFormat || ''
    }
  }
  let sData: any = { subObj: {}, positionType, salaryObj, salary }
  // 职位重新选择时重新获取模板
  if (isGetTem && $.isArrayVal(occIdArr)) {
    const nTemSubObj = await geTemplateSubWithDel(occIdArr)
    const { template: nTem, subObj: nSubObj } = nTemSubObj
    if ($.isObjVal(nTem)) {
      template = { ...nTem }
      sData.template = { ...nTem }
    }
    sData.subObj = $.isEmptyObject(nSubObj) ? {} : nSubObj
  } else if ($.isEmptyObject(template)) {
    const nOccIdArr = `${query.occIds}`.split(',').map(id => Number(id)).filter(id => id)
    if ($.isArrayVal(nOccIdArr)) {
      const nTem = await geTemplate(nOccIdArr)
      if ($.isObjVal(nTem)) {
        template = { ...nTem }
        sData.template = { ...nTem }
      }
    }
  }

  const { occId } = template || {}

  if ($.isObjVal(publishFlow) && origin == 'add') {
    const { subStep } = publishFlow || {}
    sData.subBtnTxt = '发布简历'
    if (subStep && subStep < 3) {
      // 必须更新我的找活信息数据
      await getResumeDetails()
      sData.subBtnTxt = '保存'
      // 初始化已有数据
      const perRes = await getPerInfo(occId, template, null, ext, fns)
      sData = { ...sData, ...perRes }
    }
  } else if (origin == 'edit') {
    const perRes = await getPerInfo(occId, template, sData.subObj[`${occId}_${positionType}`], ext, fns)
    sData = { ...sData, ...perRes }
  }

  console.log('template:', template)
  if ($.isEmptyObject(template)) {
    setData && setData((prev) => ({ ...prev, ...sData, ...(saveParams || {}) }))
    return
  }

  const temMust = temMustHandle(template, codeObj, sData.positionType || positionType, origin)
  console.log('temMust:', temMust)

  setData && setData((prev) => ({ ...prev, ...saveParams, ...sData, ...temMust }))
}

/**  模板必填项判断 */
// eslint-disable-next-line sonarjs/cognitive-complexity
export const temMustHandle = (template, codeObj, positionType, origin) => {
  if ($.isEmptyObject(template)) {
    return {}
  }
  const { templateInfo } = template || {}
  const { controlInfoList } = templateInfo || {}
  const mustObj: any = {}
  const preMustObj: any = {}
  const mustkey = origin == 'edit' ? 'ifManageMust' : 'ifCompleteMust'
  if ($.isArrayVal(controlInfoList)) {
    controlInfoList.forEach(ct => {
      let oObj: any = {}
      if (mustObj.F_Preference && (ct.ifStandard || ct.controlCode == 'F_Preference')) {
        oObj = mustObj.F_Preference
      }
      if (codeObj.includes(ct.controlCode)) {
        if (ct.controlCode == 'F_WorkType') {
          oObj.must = true
        }
        mustObj[ct.controlCode] = {
          must: ct[mustkey] || oObj.must || false,
          status: ct.status == 1,
        }
      }
      const natureCodeArr = positionType == 1 ? ['FULL_TIME', 'ALL'] : ['PART_TIME', 'ALL']
      if (ct.ifStandard && !oObj.must && ct.status == 1 && natureCodeArr.includes(ct.controlNatureCode)) {
        mustObj.F_Preference = {
          ...oObj,
          must: ct[mustkey] || oObj.must || false,
        }
      }

      if (ct.ifStandard && natureCodeArr.includes(ct.controlNatureCode)) {
        preMustObj[ct.controlCode] = {
          must: ct[mustkey] || false,
          status: ct.status == 1,
        }
      }
    })
  }

  // const isOk = Object.values(preMustObj).find((obj:any) => obj.status)
  // if (!isOk) {
  //   mustObj.F_Preference = {
  //     status: false,
  //     must: false,
  //   }
  // }
  return { mustObj, preMustObj }
}

/** 发布简历 获取简历求职期望信息 */
// eslint-disable-next-line sonarjs/cognitive-complexity
export async function getPerInfo(occId, template, subObj, ext: any = {}, fns:any = {}) {
  const { query, occAreaReq, userSelectedSalary } = ext
  const { origin, occIds, industries, mode } = query
  const { setQuery } = fns || {}
  let sub
  let occIdArr: Array<string> = []
  const sData: any = {}
  if (origin == 'add') {
    const resumeUuid = $.getObjVal(store.getState().storage.myResumeDetails, 'resumeUuid')
    const [data] = await $.request['POST/resume/v3/publish/findAllSub']({ resumeUuid }, { isNoToken: true })
    const { subResps } = data || {}
    if ($.isArrayVal(subResps)) {
      sub = subResps.find(s => s.occId == occId)
      occIdArr = subResps.map(sub => `${sub.occId}`)
    }
  } else if (subObj) {
    // eslint-disable-next-line sonarjs/no-gratuitous-expressions
    sub = { ...(subObj || {}) }
  } else {
    occIdArr = (occIds || '').split(',')
    const { resumeSubUuid } = query
    if (resumeSubUuid) {
      const [data] = await $.request['POST/resume/v3/perfect/findSub']({ resumeSubUuid })
      sub = { ...(data || {}) }
      sData.oSub = { ...sub }
    }
  }
  if ($.isObjVal(sub)) {
    const { resumeSubUuid, positionType } = sub || {}
    let { numFull, numPart } = query || {}
    if (positionType == 1 && numFull == 0) {
      numFull = 1
    } else if (positionType == 2 && numPart == 0) {
      numPart = 1
    }
    const nQuery: any = { ...query, numFull, numPart }
    sData.positionType = positionType
    if (!subObj) {
      nQuery.resumeSubUuid = resumeSubUuid
    }
    setQuery && setQuery((prev) => ({ ...prev, ...nQuery }))
    if ($.isEmptyObject(occAreaReq) || !$.isArrayVal(occAreaReq.occupations)) {
      const occArr = await $.getClassifyOfIndByIds(occIdArr)
      const occObj = {}
      occArr.forEach((value: any) => {
        occObj[value.id] = value
      })
      const occupations = occIdArr.map(occid => {
        const occ = occObj[occid]
        if (occ) {
          let nIndustries = []
          if (industries) {
            nIndustries = industries.split(',')
          } else {
            nIndustries = occ.industrys
          }
          return {
            occupation: `${occ.id}`,
            industries: nIndustries,
            mode: mode || occ.mode,
            name: occ.name,
          }
        }
        return null
      }).filter(item => item)
      sData.occAreaReq = { ...(occAreaReq || {}), occupations }
    }
    const otherData = handleTemData(template, userSelectedSalary, sub)
    return { ...sData, ...otherData }
  }
  return {}
}

// 模版和子简历数据(包含删除)
export const geTemplateSubWithDel = async (occIds:Array<any> = []) => {
  if (!$.isArrayVal(occIds)) {
    return {}
  }
  const resumeUuid = $.getObjVal(store.getState().storage.myResumeDetails, 'resumeUuid')
  const [data] = await $.request['POST/resume/v3/template/findWithSubListWithDel']({ resumeUuid, occIds })
  const { templates, subs } = data || {}
  const sData: any = {}
  if ($.isArrayVal(templates)) {
    sData.template = { ...templates[0] }
  }
  sData.subObj = {}
  if ($.isArrayVal(subs)) {
    subs.forEach(item => {
      sData.subObj[`${item.occId}_${item.positionType}`] = item
    })
  }
  occIds.forEach(id => {
    if (!sData.subObj[`${id}_1`]) {
      sData.subObj[`${id}_1`] = {}
    }
    if (!sData.subObj[`${id}_2`]) {
      sData.subObj[`${id}_2`] = {}
    }
  })
  return sData
}

// 获取模板信息
export const geTemplate = async (occIds: Array<number> = []) => {
  if (!$.isArrayVal(occIds)) {
    return {}
  }
  const [data] = await $.request['POST/resume/v3/template/findWithUser']({ occIds })
  const { templates } = data || {}
  if ($.isArrayVal(templates)) {
    return { ...templates[0] }
  }
  return {}
}

// 处理模板数据
export const handleTemData = (template, userSelectedSalary, sub: any = {}) => {
  const sData: any = {}
  const { positionType, occCtrls } = sub || {}
  if ($.isArrayVal(occCtrls)) {
    const { templateInfo } = template || {}
    const { controlInfoList = [], code } = templateInfo || {}
    const handleData: any = {}
    sData.preferenceList = occCtrls.filter((item: any) => {
      if (handleData[item.controlCode]) {
        return false
      }
      handleData[item.controlCode] = item.controlCode
      if (!['F_SalaryMonth', 'F_SalaryDay'].includes(item.controlCode)) {
        if ($.isArrayVal(controlInfoList) && item.controlValues) {
          const nControlInfoList = controlInfoList.map((ct: any) => {
            if (ct.controlCode == item.controlCode) {
              const { controlAttr, controlNatureCode, orderAsc } = ct || {}
              item.controlNatureCode = controlNatureCode
              item.orderAsc = orderAsc
              item.label = ct.controlName
              item.templateCode = code || item.templateCode
              const { dataList, labelList } = controlAttr || {}
              const dataObj = handleDataList(dataList)
              item.controlNames = getControlNames(item, labelList, dataObj)
            }
            return ct
          })
          sData.template = { ...template, templateInfo: { ...templateInfo, controlInfoList: nControlInfoList } }
        }
        return true
      }
      if (!userSelectedSalary[positionType]
        && ((positionType == 1 && item.controlCode == 'F_SalaryMonth') || (positionType == 2 && item.controlCode == 'F_SalaryDay'))) {
        item.templateCode = code || item.templateCode
        sData.salaryObj = item
        sData.salary = getSalaryText(item.controlValues)
      }
      return false
    }).sort((a, b) => a.orderAsc - b.orderAsc)
  }
  return sData
}

const getControlNames = (item, labelList, dataObj) => {
  if (['INPUT_STRING', 'INPUT_NUMBER'].includes(item.controlTypeCode)) {
    return item.controlValues
  }
  if (['INPUT_WITH_UNIT'].includes(item.controlTypeCode)) {
    return `${item.controlValues}${dataObj.unit || ''}`
  }
  const codeArr = item.controlValues.split(',')
  return codeArr.map(code => (labelList.find(l => l.code == code) || {}).name).filter(n => n).join('、')
}
