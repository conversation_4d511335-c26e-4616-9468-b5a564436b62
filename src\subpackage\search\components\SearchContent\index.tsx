import React from 'react'
import s from './index.module.scss'
import Click from '@/components/Click'
import IconFont from '@/components/IconFont'

interface SearchContentProps {
  title: string; // 标题
  list: string[]; // 历史记录列表
  onClear: () => void; // 清空历史记录回调
  onClick: (item: string, index: number) => void; // 点击标签回调
}

const SearchContent: React.FC<SearchContentProps> = ({
  title,
  list,
  onClear,
  onClick,
}) => {
  return (
    !!list.length
    && <V className={s.SearchContent}>

      <V className={s.header}>
        <T className={s.title}>{title}</T>
        <T className={s.clearHistory} onClick={onClear}>
          <IconFont size={28} type='yp-shanchu' className={s.icon} />
          清空历史
        </T>
      </V>
      <V className={s.list}>
        {list.length > 0
          ? list.map((item, index) => <Click opacity={false} time={1000} key={index} className={s.tag} onClick={() => onClick(item, index)} >{item}</Click>)
          : <T className={s.emptyMessage}>暂无历史记录</T>
        }
      </V>
    </V>
  )
}

export default SearchContent
