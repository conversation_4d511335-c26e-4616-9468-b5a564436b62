import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>er<PERSON>iewColumn, StandardProps as sp } from '@tarojs/components'
import cn from 'classnames'
import { useEffect, useState } from 'react'
import s from './index.module.scss'
import IconFont from '@/components/IconFont'
import PopHeader from '@/components/base/PopHeader'
import { useSelector } from '@/core/store'
import Popup from '@/components/Popup'

type Props = {
  /** 默认选中的民族code */
  value?: number
  /** 弹窗标题 */
  visible: boolean
  /** 取消事件 */
  onCancel(): void
  /** 刷新事件 */
  refresh(): void
}

export default function Index(props: Props) {
  const { visible = false } = props

  const nationList = useSelector((state) => state.global.nationList)
  const [selectIndex, setSelectIndex] = useState([0])
  const [isSubmit, setIsSubmit] = useState(true)

  useEffect(() => {
    if (!visible) {
      return
    }
    let index = nationList.findIndex((item) => item.code === props.value)
    index = index < 0 ? 0 : index // 找不到就默认选中第一项
    setSelectIndex([index])
  }, [props.value, visible])

  const onChange = ({ detail: { value } }) => {
    setSelectIndex(value)
  }

  const onSubmit = async () => {
    if (!isSubmit) {
      return
    }
    const data = nationList[selectIndex[0]]
    if (data.code == props.value) {
      props.onCancel()
      return
    }
    $.showLoading('设置中...')
    const [, res] = await $.request['POST/account/v1/userBase/updateNation']({
      nationCode: data.code,
      nationName: data.name,
    }).catch((err) => err)
    $.hideLoading()
    if (res.code != 0) {
      $.msg(res.message || '设置失败')
      props.onCancel()
      return
    }
    props.refresh()
  }

  return (<Popup visible={visible} disableScroll catchMove onClose={props.onCancel}>
    <V className={s.body}>
      <PopHeader title="请选择民族" onCancel={props.onCancel} onConfirm={onSubmit} />
      <PickerView
        indicatorStyle="border-radius: 8px;background: rgb(245, 246, 250);z-index:0"
        style="width: 100%; height: 480rpx;"
        onChange={onChange}
        value={selectIndex}
        onPickStart={() => setIsSubmit(false)}
        onPickEnd={() => setIsSubmit(true)}
      >
        <PickerViewColumn>
          {nationList.map((item, index) => (<V
            key={index} className={s.pickerItem}
          >{item.name}</V>))}
        </PickerViewColumn>
      </PickerView>
    </V>
  </Popup>)
}
