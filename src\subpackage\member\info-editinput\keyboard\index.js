
Component({
  didMount() {
    $.taro.removeStorage({ key: 'keyboardH' })
  },
  data: {
    text: '',
    height: 0,
  },
  rootEvents: {
    onKeyboardHeight(e) {
      if (this.data.height) {
        return
      }
      const { pixelRatio, platform } = $.taro.getSystemInfoSync()
      const plat = platform.toLowerCase()
      let height = e.height
      if (plat !== 'ios' && plat !== 'iphone') {
        height = e.height / pixelRatio // 安卓手机需要除去像素比
      }
      $.taro.setStorage({ key: 'keyboardH', data: height })
    }
  },
})
