import { WebView } from '@tarojs/components'
import { useLoad } from '@tarojs/taro'
import { useState, useEffect, useRef } from 'react'
import { useSelector } from '@/core/store'
import { getH5OfHeader } from './utils'
import { MOBILE_TERMINAL_NEW_URL } from '@/core/config'
import Page from '@/components/Page'
/**
 * 参数说明:
 * @param {string} url - H5 路由地址。如果不带 https 则默认为内部 H5 域名。
 * @param {boolean} [isLogin=false] - 是否需要携带登录信息。true: 需要；false: 不需要；可以不传，默认 false。
 */
interface UpdateParams {
  isHeader: boolean
  isSession?: boolean
  isBottom?: boolean
  isUid?: boolean
}

export default function WebViewPage() {
  const [webViewSrc, setWebViewSrc] = useState('') // WebView 的 URL
  const [shouldRenderWebView, setShouldRenderWebView] = useState(false) // 是否加载 WebView
  const isUserLoggedIn = useSelector((state) => !!Object.values(state.storage.userInfo)?.length && !!state.storage.token) // 用户是否登录
  const prevLoginStatus = useRef<boolean>(isUserLoggedIn) // 上一次登录状态

  const updateWebViewSrc = async (baseUrl: string, extraParams: UpdateParams): Promise<string> => {
    const h5params = await getH5OfHeader(extraParams)
    const connector = baseUrl.includes('?') ? '&' : '?'
    return `${baseUrl}${connector}${h5params}`
  }

  useLoad(async (options: { url: string; isLogin?: string }) => {
    const { url, isLogin = 'false' } = options
    const parsedUrl = url.startsWith('http') ? decodeURIComponent(url) : `${MOBILE_TERMINAL_NEW_URL}/${decodeURIComponent(url)}`

    const finalUrl = await updateWebViewSrc(parsedUrl, { isHeader: true, isSession: true, isBottom: true })
    setWebViewSrc(finalUrl)

    if (isLogin === 'true') {
      !isUserLoggedIn && $.login()
      setShouldRenderWebView(isUserLoggedIn)
    } else {
      setShouldRenderWebView(true)
    }
  })

  useEffect(() => {
    // 只在未登录 -> 已登录 时执行
    if (prevLoginStatus.current === isUserLoggedIn) {
      return
    }
    prevLoginStatus.current = isUserLoggedIn // 更新上一次登录状态
    if (isUserLoggedIn) {
      (async () => {
        const updatedUrl = await updateWebViewSrc(webViewSrc, { isHeader: true, isSession: true, isBottom: true, isUid: true })
        setWebViewSrc(updatedUrl)
        setShouldRenderWebView(true)
      })()
    }
  }, [isUserLoggedIn, webViewSrc])

  return (
    <Page>
      {shouldRenderWebView && (
        <WebView src={webViewSrc} />
      )}
    </Page>
  )
}
