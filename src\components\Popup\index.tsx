/*
 * @Date: 2024-11-28 19:21:01
 * @Description: Popup
 */

import { useEffect } from 'react'
import type { StandardProps } from '@tarojs/components/types/props'
import cn from 'classnames'
import Mask, { MaskProps } from './Mask'
import useReducer from '@/hooks/useReducer'
import s from './index.module.scss'
import IconFont from '../IconFont'

type Props = MaskProps & {
  children: React.ReactNode
  /** 点击关闭回调 */
  onClose?: () => void
  /** 弹出位置 */
  position?: 'top' | 'bottom'
  /** 是否显示关闭按钮，默认false */
  closeable?: boolean
  /** 左边的节点 */
  left?: React.ReactNode
  /** 中间的节点 */
  title?: React.ReactNode
  /** 右边的节点 */
  right?: React.ReactNode
  /** 显示头部默认false */
  showHeader?: boolean
  /** content的className */
  contentClass?: StandardProps['className']
  /** content的style */
  contentStyle?: string
  /** 是否禁止滑动 */
  disableScroll?: boolean
}

type InitState = {
  render: boolean
  show?: boolean
}

export default (props: Props) => {
  const { children,
    visible,
    onClose,
    contentStyle = '',
    contentClass = '',
    position,
    left,
    right,
    title,
    closeable,
    catchMove,
    disableScroll = false,
    maskClose,
    zIndex,
    showHeader,
  } = props

  const [{ render, show }, dispatch] = useReducer<InitState>({ render: false })

  useEffect(() => {
    let timer: ReturnType<typeof setTimeout>
    if (visible) {
      dispatch({ render: true })
      timer = setTimeout(() => {
        dispatch({ show: true })
      }, 50)
    } else {
      dispatch({ show: false })
      timer = setTimeout(() => {
        dispatch({ render: false })
      }, 200)
    }
    return () => timer && clearTimeout(timer)
  }, [visible])
  // transform: translateY(${show ? 0 : 100}%)
  return (
    <Mask catchMove={catchMove} disableScroll={disableScroll} visible={render} onClose={onClose} maskClose={maskClose} zIndex={zIndex}>
      <V className={cn(s.content, contentClass)} style={`transform: translateY(${show ? 0 : 100}%);${contentStyle}`} onClick={(e) => e.stopPropagation()}>
        {
          showHeader && (
            <V className={s.header}>
              { left && <V className={s.left}> {left} </V>}
              { title && <V className={s.title}> {title} </V> }
              {
                (closeable || right) && (
                  <V className={s.right}>
                    { right || <IconFont type="yp-close-small" size={48} color="#000000A6" className={s.close} onClick={onClose} />}
                  </V>
                )
              }
            </V>
          )
        }
        {children}
      </V>
    </Mask>
  )
}
