.item {
  padding: 32rpx 0;
}

.label {
  display: flex;
  align-items: center;
  color: rgba(0, 0, 0, 0.85);
  font-weight: bold;
  font-size: 38rpx;
  line-height: 54rpx;
}

.must {
  font-size: 28rpx;
  color: rgba(232, 54, 46, 1);
  line-height: 40rpx;
}

.inpView {
  padding: 32rpx 24rpx;
  border-radius: 16rpx;
  border: 2rpx solid rgba(233, 237, 243, 1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 34rpx;
  line-height: 48rpx;
  margin-top: 24rpx;
}

.inp {
  color: rgba(0, 0, 0, 0.85);
  width: 100%;
  margin-right: 32rpx;
}

.inpPlace {
  color: rgba(0, 0, 0, 0.25);
}

.inpNumView {
  color: rgba(0, 0, 0, 0.25);
  font-size: 30rpx;
  line-height: 42rpx;
}

.inpNumGray {
  color: rgba(0, 146, 255, 1);
}

.clear {
  margin-right: 32rpx;
}
