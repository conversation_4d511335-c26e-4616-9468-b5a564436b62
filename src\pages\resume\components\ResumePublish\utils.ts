import { getResumeDetails } from '@/utils/helper/resume/index'
import { publishResumeResultReport } from '../../utils'

// 验证字段
export function validationForm({ hopeArea, occupations }) {
  // 验证是否有选择城市
  if (!$.isArrayVal(hopeArea)) {
    $.msg('请选择工作城市')
    return false
  }
  // 验证是否有选择工种
  if (!$.isArrayVal(occupations)) {
    $.msg('请选择期望职位')
    return false
  }
  return true
}

/** 是否已跳转页面 */
export const judgeJumpPage = (data) => {
  const { publishFlow, template, userInfo, params, publishRuleSwitch, isBack, isPopStyle } = data

  const toJump = isBack ? $.router.replace : $.router.push
  if ($.isObjVal(template) && $.isObjVal(userInfo)) {
    if (publishFlow.perfects[0] == 1) {
      $.hideLoading()
      toJump('/subpackage/resume-complete/one/index', {}, {
        publishFlow,
        params,
        template,
        userInfo,
        publishRuleSwitch,
        isBack,
        isPopStyle,
      })
      return true
    }
    if (publishFlow.perfects[1] == 1) {
      $.hideLoading()
      toJump('/subpackage/resume-complete/two/index', {}, {
        publishFlow,
        params,
        template,
        publishRuleSwitch,
        isBack,
        isPopStyle,
      })
      return true
    }
  }
  return false
}

/** 发布简历 处理结果 */
export async function resultProcessing(state, params, fnExt?: any) {
  const { occAreaReq } = params || {}
  // 刷新找活名片
  await getResumeDetails()
  await $.msg('发布成功')
  fnExt?.publishSuccess?.()
  publishResumeResultReport('0', '1', occAreaReq, { published_source_id: state?.isPopStyle ? '1' : '' })
  const isJudge = judgeJumpPage({ ...state, params })
  if (isJudge) return
  const { isBack } = state || {}
  if (isBack) {
    $.router.back()
    return
  }
  // 发布简历回到首页需要更新信息流
  const pdata: any = { origin: 'resume', listType: 2 }
  // const { occAreaReq } = params || {}
  const { hopeArea } = occAreaReq || {}
  if ($.isArrayVal(hopeArea)) {
    pdata.areaId = `${hopeArea[0]}`
  }
  $.router.push('/pages/index/index', {}, pdata)
}
