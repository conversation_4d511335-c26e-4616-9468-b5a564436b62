import { store } from '@/core/store/index'
import { getResumeDetails } from '@/utils/helper/resume/index'
import storage from '@/store/storage/storage'
import { handlePublishResult } from '@/core/utils/publish'
import { publishResumeResultReport } from '@/pages/resume/utils'

/** 求职期望提交校验 */
// eslint-disable-next-line sonarjs/cognitive-complexity
export function formatVal(data) {
  const { positionType, preferenceList, salaryObj,
    mustObj, preMustObj, isClsChange, occNames } = data
  let wt = mustObj.F_WorkType
  if (isClsChange && wt && wt.status && wt.must && !occNames) {
    $.msg('请选择期望职位')
    return true
  }
  wt = positionType == 1 ? mustObj.F_SalaryMonth : mustObj.F_SalaryDay
  if (wt && wt.status && wt.must && ($.isEmptyObject(salaryObj) || salaryObj.controlValues == '')) {
    $.msg('请选择期望薪资')
    return true
  }
  wt = mustObj.F_Preference
  if (wt.status && wt.must) {
    const preKeys = Object.keys(preMustObj)
    let mPres: Array<any> = []
    // 职业偏好具体子项是否有必填项
    let cMust = false
    if ($.isArrayVal(preKeys)) {
      mPres = preKeys.filter(ky => {
        const oj = preMustObj[ky]
        if (oj.status && oj.must) {
          cMust = true
          const item = preferenceList.find(pf => pf.controlCode === ky)
          if (item && item.controlValues) {
            return true
          }
        }
        return false
      })
      // 如果职业偏好是必填，但是职业偏好子项没有必填项，判断子项是否有选中值
      if (!cMust && mPres.length == 0 && $.isArrayVal(preferenceList)) {
        mPres = [...preferenceList]
      }
      if (!$.isArrayVal(mPres)) {
        $.msg('请完善职业偏好')
        return true
      }
    }
  }
  return false
}

/** 发布简历 新增求职期望 */
// eslint-disable-next-line sonarjs/cognitive-complexity
export async function publishOrUpdateSub(data) {
  if (formatVal(data)) {
    return
  }
  const { positionType, preferenceList, salaryObj, occAreaReq, userReq, publishFlow, params: eparams, isBack } = data
  const { occupations } = occAreaReq || {}
  const { subStep } = publishFlow || {}
  const occs: Array<any> = []
  if ($.isArrayVal(occupations)) {
    occupations.forEach((occ) => {
      occs.push({
        occupation: occ.id || occ.occupation,
        industries: occ.hids || occ.industries,
        mode: occ.mode,
      })
    })
  }
  let nParams: any = {}
  let occCtrlReqList: Array<any> = []
  if ($.isArrayVal(preferenceList)) {
    occCtrlReqList = [...preferenceList]
  }
  if ($.isObjVal(salaryObj)) {
    occCtrlReqList.push(salaryObj)
  }

  $.showLoading('请求中...')
  let isPublish = false
  let rqPath = ''
  if (subStep && subStep < 3) {
    const resumeUuid = $.getObjVal(store.getState().storage.myResumeDetails, 'resumeUuid')
    nParams.resumeUuid = resumeUuid
    // 保存
    nParams.positionType = positionType
    if ($.isArrayVal(occCtrlReqList)) {
      nParams.occCtrls = occCtrlReqList
    }
    if ($.isArrayVal(occs)) {
      nParams.occupationIndustries = occs
    }
    rqPath = 'POST/resume/v3/publish/updateSub'
  } else {
    isPublish = true
    // 发布简历
    const subPublishReq: any = { positionType }
    if ($.isObjVal(occAreaReq)) {
      nParams.occAreaReq = occAreaReq
    }
    if ($.isArrayVal(occs)) {
      subPublishReq.occupations = occs
    }
    if ($.isObjVal(userReq)) {
      nParams.userReq = userReq
    }
    if ($.isArrayVal(occCtrlReqList)) {
      subPublishReq.occCtrlReqList = occCtrlReqList
    }
    nParams.subPublishReq = subPublishReq
    nParams = { ...eparams, ...nParams }
    rqPath = 'POST/resume/v3/publish/publish'
  }
  $.request[rqPath](nParams, { hideMsg: true }).then(async () => {
    $.hideLoading()
    storage.removeSync('pubishData')
    // 必须更新我的找活信息数据
    await getResumeDetails()
    if (isPublish) {
      await $.msg('发布成功')
      publishResumeResultReport('0', '1', occAreaReq)
    }
    if (isBack) {
      $.router.back()
      return
    }
    const pdata:any = { origin: 'resume', listType: 2 }
    const { hopeArea } = occAreaReq || {}
    if ($.isArrayVal(hopeArea)) {
      pdata.areaId = `${hopeArea[0]}`
    }
    $.router.push('/pages/index/index', {}, pdata)
  }).catch((res) => {
    $.hideLoading()
    handlePublishResult((res[1] || {}), isPublish ? '发布' : '修改')
    isPublish && publishResumeResultReport('0', '0', occAreaReq)
  })
}

/** 我的简历 新增和编辑期望工种 */
// eslint-disable-next-line sonarjs/cognitive-complexity
export async function addOrUpdateSub(data, query) {
  if (formatVal(data)) {
    return
  }
  const { salaryObj, positionType, preferenceList, occAreaReq, template } = data
  // const { salaryObj } = this.data
  const { type, resumeSubUuid } = query
  const { occupations } = occAreaReq || {}
  const params:any = { positionType }
  let occCtrlReqList:Array<any> = []
  if ($.isArrayVal(preferenceList)) {
    occCtrlReqList = [...preferenceList]
  }
  if (!$.isEmptyObject(salaryObj)) {
    if (!salaryObj.controlCode) {
      const { templateInfo } = template || {}
      const { controlInfoList = [], code } = templateInfo || {}
      if ($.isArrayVal(controlInfoList)) {
        const control = controlInfoList.find(ctr => ctr.controlCode == (positionType == 1 ? 'F_SalaryMonth' : 'F_SalaryDay'))
        const { controlCode, controlTypeCode } = control || {}
        salaryObj.templateCode = code
        salaryObj.controlCode = controlCode
        salaryObj.controlTypeCode = controlTypeCode
      }
    }
    if (salaryObj.controlCode) {
      occCtrlReqList.push(salaryObj)
    }
  }
  if ($.isArrayVal(occCtrlReqList)) {
    params.occCtrls = occCtrlReqList
  }
  occupations.forEach(item => {
    // 过滤item.industries中为-1的值
    item.industries = $.isArrayVal(item.industries) ? item.industries.filter(industries => industries !== -1) : []
  })
  $.showLoading('请求中...')
  let isPublish = false
  let rqPath = ''
  if (type == 1) {
    params.occupations = occupations
    const resumeUuid = $.getObjVal(store.getState().storage.myResumeDetails, 'resumeUuid')
    params.resumeUuid = resumeUuid
    rqPath = 'POST/resume/v3/perfect/addSub'
    isPublish = true
  } else {
    if ($.isArrayVal(occupations)) {
      params.occupations = { ...occupations[0] }
    }
    params.resumeSubUuid = resumeSubUuid
    rqPath = 'POST/resume/v3/perfect/updateSub'
  }
  $.request[rqPath](params, { hideMsg: true }).then(() => {
    $.hideLoading()
    $.router.back()
  }).catch((res) => {
    $.hideLoading()
    handlePublishResult(res[1] || {}, isPublish ? '添加' : '修改')
  })
}

/** 删除求职期望 */
export async function delSub(query) {
  const { resumeSubUuid } = query || {}
  if (resumeSubUuid) {
    $.showLoading('请求中...')
    $.request['POST/resume/v3/perfect/delSub']({ resumeSubUuid }, { hideMsg: true }).then(() => {
      $.hideLoading()
      $.router.back()
    }).catch((res) => {
      $.hideLoading()
      handlePublishResult(res[1], '删除')
    })
  }
}
