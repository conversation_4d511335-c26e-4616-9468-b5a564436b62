import { View, Image, Text } from '@tarojs/components'
import { Card } from '../Card'
import styles from './index.module.scss'
// import useClick from '@/hooks/useClick'

export default () => {
  // const jumpToSecurity = useClick(async () => {
  //   $.router.push('/subpackage/web-view/index', {
  //     isLogin: true,
  //     fromPage: 'news',
  //     url: '%2Ffraud-prevention-guide%3Ftype%3Dmini%3FactiveKey%3D0',
  //   })
  // })

  // isLogin=true&fromPage=news&url=%2Ffraud-prevention-guide%3Ftype%3Dmini

  return <Card>
    <View className={styles.title}>
      <Image src="https://cdn.yupaowang.com/yupao_mini/yp-secrity-icon.png" className={styles.img} mode="aspectFill" /><Text>鱼泡直聘安全保障</Text>
    </View>
    <View className={styles.content}>如遇到办证收费、刷单、传销、诱导买车等违规行为，请立即向鱼泡网投诉举报</View>
  </Card>
}
