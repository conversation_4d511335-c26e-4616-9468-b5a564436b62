import { Text, View } from '@tarojs/components'
import { useRef, useState } from 'react'
import { useLoad } from '@tarojs/taro'
import Page from '@/components/Page'
import { getListValue, handleDataList } from '@/core/utils/publish'
import style from './index.module.scss'
import ResumeTemplatesV4 from './components/ResumeTemplatesV4'
import Click from '@/components/Click'
import CustomNavbar from '@/components/CustomNavbar'

export default function Index() {
  const { template, preferenceList } = $.router.data

  const [data, setData] = useState<any>({
    resumeTl: {},
    /** 未修改值的提交数据 */
    oldValue: [],
    // 模板的控件 只存controlCode
    controlList: [],
    // 模板的控件 对应对象，controlCode为键值
    controlObj: {} as any,
    // 模板对应的工种
    classify: {},
    // 模板的code
    templateCode: '',
    mustKey: 'ifManageMust',
    query: {
      /** 工种id,多个用,分割 */
      occIds: '',
      /** 来源页  add:简历发布  edit:编辑简历 */
      origin: 'add',
      positionType: 1,
    },
  })
  const changeList = useRef({})

  useLoad((options) => {
    init(options)
  })

  const init = async (query) => {
    let nTemplate = { ...template }
    if ($.isEmptyObject(template)) {
      const occIds = `${query.occIds}`.split(',').map(id => Number(id))
      if (!$.isArrayVal(occIds)) {
        return
      }
      const [data] = await $.request['POST/resume/v3/template/findWithUser']({ occIds })
      const { templates } = data || {}

      if ($.isArrayVal(templates)) {
        nTemplate = { ...templates[0] }
      }
    }
    if ($.isEmptyObject(nTemplate)) {
      return
    }
    const { positionType, origin } = query
    const { templateInfo, occId } = nTemplate || {}
    const { controlInfoList, code } = templateInfo || {}
    const controlObj: any = {}
    const controlList = controlInfoList.map(ct => {
      const natureCodeArr = positionType == 1 ? ['FULL_TIME', 'ALL'] : ['PART_TIME', 'ALL']
      if (ct.ifStandard && ct.status && natureCodeArr.includes(ct.controlNatureCode)) {
        const nct = { ...ct }
        const { controlAttr } = nct || {}
        const { dataList, labelList } = controlAttr || {}
        if ($.isArrayVal(preferenceList)) {
          const item = preferenceList.find(pf => pf.controlCode == ct.controlCode)
          if (item) {
            if (['INPUT_STRING', 'INPUT_NUMBER', 'INPUT_WITH_UNIT'].includes(item.controlTypeCode)) {
              nct.inputValue = item.controlValues
            } else if ($.isArrayVal(labelList)) {
              const codeArr = item.controlValues.split(',')
              const nLabelList = labelList.map((lb: any) => {
                const nlb = { ...lb }
                nlb.checked = codeArr.includes(lb.code)
                return nlb
              })
              controlAttr.labelList = nLabelList
            }
          }
        }
        controlAttr.dataObj = handleDataList(dataList)
        nct.controlAttr = controlAttr
        controlObj[ct.controlCode] = nct
        return ct.controlCode
      }
      return null
    }).filter(Boolean)
    const sData: any = { controlObj, controlList, templateCode: code, oldValue: preferenceList }
    const classifys = await $.getClassifyOfIndByIds([occId])
    if ($.isArrayVal(classifys)) {
      sData.classify = { ...(classifys[0] || {}) }
    }
    setData((prev) => ({ ...prev, ...sData, query, mustKey: origin == 'add' ? 'ifCompleteMust' : 'ifManageMust' }))
  }

  const onChange = (e) => {
    const { list } = e || {}
    changeList.current = list
  }

  /** 判断是否可以提交数据 */
  const getSubmitParams = async () => {
    const { oldValue } = data
    const { templateCode, mustKey } = data
    const { value, isErrMsg } = getListValue({ list: Object.values(changeList.current || {}), reqAll: false, mustKey, templateCode, notIfMust: false, isHideMsg: false })

    if (isErrMsg) {
      return false
    }

    // 判断值是否有变化  两个对象是否相等
    if ($.isArrayVal(value) && $.isEqualObj(oldValue, value)) {
      $.router.back(1)
      return false
    }
    return value
  }

  /** 提交数据 */
  const onSubmit = async () => {
    const params = await getSubmitParams()
    if (!params) {
      return
    }
    $.router.event(params)
    $.router.back(1)
  }
  return (
    <Page backgroundColor='#fff'>
      <CustomNavbar title=' ' />
      <View className={style.body}>
        <View className={style.headv}>
          <View className={style.headTile}>{data.classify.name}岗位求职偏好</View>
          <View className={style.headDesc}>你的偏好选择将用于为你推荐更匹配的职位</View>
        </View>
        <ResumeTemplatesV4
          controlList={data.controlList}
          controlObj={data.controlObj}
          mustKey={data.mustKey}
          change={onChange}
        />
      </View>
      <View className={style.footer}>
        <View className={style.btnView}>
          <Click className={style.btn} onClick={onSubmit}><Text>保存</Text></Click>
        </View>
      </View>
    </Page>
  )
}
