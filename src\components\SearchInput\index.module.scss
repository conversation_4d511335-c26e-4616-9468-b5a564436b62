
.searchInput {
  align-items: center;
  display: flex;
  position: relative;
  width: 100%;
}

.inputBox {
  flex:1;
  align-items: center;
  display: flex;
  border-radius: 16px;
  padding: 10px 24px;
  background-color: #F5F7FCFF;
}


.disableInput {
  flex: 1;
  background-color: #F5F7FCFF;
  padding-block: 5px;
  margin-left: 5px;
}


.leftSlot {
  margin-right: 5px;
  line-height: 1;
}


.disablePlaceHolder {
  color: #bfbfbf;
}

.input {
  flex: 1;
  border: none;
  background: transparent;
  outline: none;
  font-size: 30px;
  caret-color: #0092FFFF;
  color: rgba(0, 0, 0, 0.85);
}

.input::placeholder {
  color: #00000040;
}


.rightSlot {
  cursor: pointer;
  margin-left: 32px;
  line-height: 1;
}

.defaultSearch {
  font-size: 30px;
  color: rgba(0, 0, 0, 0.85);
}

