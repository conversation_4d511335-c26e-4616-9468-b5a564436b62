
.hidden {
  display: none!important;
}

.body {
  padding: 32px;
  background-color: #FFF;
}

.head {
  overflow: hidden;
  padding-bottom: 32px;
}

.title {
  font-size: 54px;
  font-weight: bold;
  line-height: 76px;
}

.desc {
  padding-top: 12px;
  font-size: 26px;
  line-height: 36px;
  color: rgba(0, 0, 0, 0.65);
}

.textarea {
  padding: 0;
  font-size: 30px;
  line-height: 50px;
  width: 100%;
  color: rgba(0, 0, 0, 0.85);
  display: block;
  height: calc(100vh - 160px - 260px - 50px);
  /*  - 220px */
  padding-bottom: 32px;
  word-break: break-word;
  white-space: pre-wrap;
}

.placeholder {
  word-break: break-word;
  white-space: pre-wrap;
  font-size: 30px;
  line-height: 50px;
  color: rgba(0, 0, 0, 0.45);
}

.footer {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 24px 32px;
  background-color: #FFF;
  @include safe-area(24px);
  border-top: 1px solid rgba(233, 237, 243, 1);
}

.footer-full {
  height: 220px;
  @include safe-area();
}

.disabled {
  color: rgba(0, 0, 0, 0.25) !important;
  pointer-events: none !important;
}

.info-text {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 20px;

  .clear {
    color: $primary-color;
  }
  .info-num {
    display: flex;
    align-items: center;
  }
  .num {
    color: $primary-color;
  }
  .num-err {
    color: $error-color;
  }
  .num-gray {
    color: rgba(0, 0, 0, 0.45);
  }
}

.btn {
  color: #FFF;
  font-size: 34px;
  height: 80px;
  line-height: 80px;
  display: flex;
  font-weight: bold;
  text-align: center;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  background: $primary-color;

  &.btn-mini{
    height: 72px;
    line-height: 72px;
    display: inline-flex;
    align-items: center;
    padding: 0 30px;
    margin-left: 24px;
  }
}
