.card {
  margin: 0 24px 16px 24px;
  padding: 24px;
  border-radius: 24px;
  background: #ffffff;

  &:last-child {
    margin-bottom: 0;
  }
}

.title {
  color: rgba(0, 0, 0, 0.85);
  font-weight: 500;
  font-size: 34px;
  line-height: 48px;
  margin-bottom: 24px;
  display: -webkit-box;
  overflow: hidden;
  word-break: break-all;
  line-clamp: 2;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  white-space: pre-wrap;
}

.tags {
  display: flex;
  flex-wrap: wrap;
  height: 44px;
  overflow: hidden;
}

.tag {
  padding: 0 12px;
  border-radius: 8px;
  background: #f5f7fc;
  height: 44px;
  margin: 0 8px 8px 0;
  color: #000000a6;
  font-size: 26px;
  line-height: 44px;
  white-space: nowrap;

  &.tagBlue {
    background: #e0f3ff;
    color: #0092ff;
  }
}

.footer {
  margin-top: 24px;
  display: flex;
  flex-direction: row;
  align-items: center;
  font-size: 26px;
  color: #00000073;
  width: 100%;

}
.address {
  display: flex;
  flex-shrink: 2;
  @include textrow(1);
}

.footerInfo {
  margin-left: auto;
  flex-shrink: 1;
  display: flex;
  flex-direction: row;
  word-break: keep-all;
}

.location {
  margin-right: 24px;
}

.location,.date {
  display: flex;
  flex-direction: row;
  width: max-content;
}