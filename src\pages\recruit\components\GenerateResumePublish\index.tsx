import { useEffect, useState } from 'react'
import { Text, View } from '@tarojs/components'
import cn from 'classnames'
import styles from './index.module.scss'
import IconFont from '@/components/IconFont'
import Popup from '@/components/Popup'
import { getAreaSearches } from '@/utils/location'
import { store, useSelector } from '@/core/store/index'
import { IClassifyDataRp } from '@/core/utils/index.d'
import { handlePublishResult } from '@/core/utils/publish'
import { getResumeDetails } from '@/utils/helper/resume'
import { publishResumeResultReport } from '@/pages/resume/utils'

type IProps = {
  visible: boolean
  /** 1- 抖音小程序留资1一配置招工信息ID ； 2- 抖音小程序留资2一配置工种ID ；3 - 招工详情 */
  sourceId: string
  /** 城市ID，格式:[id,id,id] */
  hopeAreas?: Array<any>
  /** 工种，格式:[{industry: 行业, occIds: [工种ID,工种ID]}] */
  occupations?: Array<any>
  onClose?: () => void
  onConfirm?: () => void
}
/**
 * 福利popup 弹窗
*/
export default (props: IProps) => {
  const { visible, hopeAreas, occupations = [], onClose, onConfirm, sourceId } = props
  const [show, setShow] = useState(false)

  const [hopeAreaObj, setHopeAreaObj] = useState<{ hopeAreas: string[], hopeAreaStr: string }>({ hopeAreas: [], hopeAreaStr: '' }) // 工作期望城市
  const [occObj, setOccObj] = useState<{ occupations: Array<IClassifyDataRp>, occsStr: string }>({ occupations: [], occsStr: '' }) // 职位

  const classifyConfig = useSelector((state) => state.classify.classifyConfig)
  const { basicConfigResp } = useSelector(state => state.resume.globalConfig)

  useEffect(() => {
    if (visible) {
      initData()
      $.report.event('one_click_resume_popup_expose', { popup_page: sourceId })
    }
    setShow(visible)
  }, [visible])

  useEffect(() => {
    initOccs()
  }, [occupations])

  const initData = async () => {
    const params: any = {}
    const { level, pid, id, latitude, longitude } = store.getState().storage.userLocation
    if (longitude && latitude) {
      params.longitude = longitude
      params.latitude = latitude
    }
    const [data] = await $.request['POST/resume/v3/publish/showDefault'](params)
    const { hopeAreas: defHopeAreas } = data || {}
    const areaId = level == 3 ? pid : id
    let nHopeAreas: Array<string> = []
    if ($.isArrayVal(defHopeAreas)) {
      nHopeAreas = defHopeAreas.map(hopeArea => hopeArea.id).filter(id => id).map((id) => `${id}`)
    } else if (areaId) {
      nHopeAreas = [`${areaId}`]
    } else if ($.isArrayVal(hopeAreas)) {
      nHopeAreas = [...(hopeAreas || [])]
    }
    const hopeAreaStr = (await getAreaSearches(nHopeAreas, 'id'))
      .filter(Boolean)
      .map(({ current }) => current && current.name)
      .filter(Boolean).join('、')
    setHopeAreaObj({ hopeAreas: nHopeAreas, hopeAreaStr })
  }

  const initOccs = async () => {
    let nOccupations: Array<IClassifyDataRp> = []
    if ($.isArrayVal(occupations)) {
      const occObj: any = {}
      const jobIds = [...new Set((occupations || []).flatMap(item => item.occIds))]
      nOccupations = await $.getClassifyOfIndByIds(jobIds)
      occupations.forEach((item) => {
        const { industry, occIds } = item || {}
        occIds.forEach((occId) => {
          if (occObj[occId]) {
            occObj[occId].push(industry)
          } else if (industry) {
            occObj[occId] = [industry]
          }
        })
      })
      nOccupations = nOccupations.map(occ => {
        const nocc = { ...occ }
        const { id = '' } = nocc
        if ($.isArrayVal(occObj[id])) {
          nocc.industries = occObj[id]
        }
        return nocc
      })
    }
    const maxSelectNum = classifyConfig.recruitWorkerSearchFindJobPublishOccCnt || 5
    setOccObj({ occupations: nOccupations.slice(0, maxSelectNum), occsStr: nOccupations.map(occ => occ.name).join('、') })
  }

  const onHidePop = () => {
    setShow(false)
    onClose && onClose()
  }

  const onCityCLick = () => {
    const { maxCityNum } = basicConfigResp || {}
    $.openAddress({
      areas: hopeAreaObj.hopeAreas,
      level: 2,
      title: '选择城市',
      maxNum: maxCityNum,
      // disabledIds: [33, 34, 35],
      hideNation: true,
    }, {
      source: '发布找活名片',
      source_id: '10',
      button_name: hopeAreaObj.hopeAreaStr,
    }, (data) => {
      if ($.isArrayVal(data)) {
        const nHopeAreas = data.map(item => `${item.id}`)
        setHopeAreaObj({ hopeAreas: nHopeAreas, hopeAreaStr: data.map(item => `${item.name}`).join('、') })
      }
    })
  }

  const onClassifyClick = () => {
    $.openClassify({
      sourceId: '8',
      sourcePageName: '发布找活',
      value: occObj.occupations,
      maxSelectNum: classifyConfig.recruitWorkerSearchFindJobPublishOccCnt || 5,
    }, (data) => {
      const occsStr = data.map((occ: any) => (occ.name || '')).filter(name => name).join('、')
      setOccObj({ occupations: data, occsStr })
    })
  }

  const onBtnConfirm = async () => {
    const occAreaReq: any = {
      occupations: occObj.occupations.map((occ) => ({
        occupation: occ.id,
        name: occ.name,
        mode: occ.mode,
        industries: occ.industries,
      })),
      hopeArea: hopeAreaObj.hopeAreas,
    }
    // 验证是否有选择城市
    if (!$.isArrayVal(occAreaReq.hopeArea)) {
      $.msg('请选择工作城市')
      return
    }
    // 验证是否有选择工种
    if (!$.isArrayVal(occAreaReq.occupations)) {
      $.msg('请选择期望职位')
      return
    }

    $.report.event('one_click_resume_popup_submit', { popup_page: sourceId, preferred_work_location: hopeAreaObj.hopeAreaStr, preferred_position: occObj.occsStr })
    $.showLoading('请求中...')
    const params: any = { occAreaReq }
    $.request['POST/resume/v3/publish/publish']({ ...params }, { hideMsg: true }).then(async () => {
      // 刷新找活名片
      await getResumeDetails()
      $.hideLoading()
      setShow(false)
      onClose && onClose()
      publishResumeResultReport('0', '1', occAreaReq)
      // await $.msg('发布成功')
      onConfirm && onConfirm()
    }).catch((res) => {
      $.hideLoading()
      setShow(false)
      onClose && onClose()
      publishResumeResultReport('0', '0', occAreaReq)
      handlePublishResult(res[1] || {}, '发布')
    })
  }
  return (
    <Popup
      visible={show}
      position="bottom"
      onClose={onHidePop}
    >
      <View className={styles.warp}>
        <View className={styles.head}>
          <Text className={styles.title}>一键生成在线简历</Text>
        </View>
        <View className={styles.item}>
          <View className={styles.label}><Text className={styles.labelTxt}>期望工作地</Text></View>
          <View className={styles.content} onClick={onCityCLick}>
            <View className={hopeAreaObj.hopeAreaStr ? styles.value : styles.placeholder}>{hopeAreaObj.hopeAreaStr || '请选择工作城市'}</View>
            <IconFont className={styles.icon} type='yp-mianbaoxue' size={32} color='rgba(0, 0, 0, 0.45)' />
          </View>
        </View>
        <View className={styles.item}>
          <View className={styles.label}><Text className={styles.labelTxt}>期望职位</Text></View>
          <View className={styles.content} onClick={onClassifyClick}>
            <View className={occObj.occsStr ? styles.value : styles.placeholder}>{occObj.occsStr || '请选择期望职位'}</View>
            <IconFont className={styles.icon} type='yp-mianbaoxue' size={32} color='rgba(0, 0, 0, 0.45)' />
          </View>
        </View>
      </View>
      <View className={styles.footer}>
        <View onClick={onBtnConfirm} className={cn(styles.btn, hopeAreaObj.hopeAreas.length > 0 && occObj.occupations.length > 0 ? styles.btnOk : styles.btnNo)}><Text className={styles.btnTxt}>立即报名</Text></View>
      </View>
    </Popup>
  )
}
