import { useEffect, useState } from 'react'
import s from './index.module.scss'
import UserInfo from './components/UserInfo'
import JobStatus from './components/JobStatus'
import Introduce from './components/Introduce'
import JobCity from './components/JobCity'
import JobResume from './components/JobResume'
import CustomNavbar from '@/components/CustomNavbar'
import { getResumeDetails } from '@/utils/helper/resume'
import { getState } from '@/core/store'
import { formatResumeDetails, handlerShowDetail } from './utils'
import { resumeExistsHandler } from '@/utils/helper/resumeFormat'

type PropsType = {
  refreshNum: number
}

/** 我的在线简历页 */
export default function ResumeDetail(props: PropsType) {
  const [isRefresh, setIsRefresh] = useState(false)

  /** 格式化之后的UI数据 */
  const [diyDetails, setDiyDetails] = useState({
    /** 用户信息 */
    userInfo: {},
    /** 找活的uuid */
    resumeUuid: '',
    /** 找活基础信息 */
    basicInfo: { hopeAreaCity: [], introduce: '' },
    /** 求职期望 */
    subJob: {
      jobFull: [],
      jobPart: [],
    },
    /** 求职状态 */
    jobStatus: {
      /** 求职状态 */
      workStatus: 1,
      /** 求职状态名称 */
      workStatusName: '',
    },
  })

  const [showElem, setShowElem] = useState({
    isIntroduce: true, // 个人优势
    isCity: true, // 工作城市
    isWork: false, // 工作经历
    isProject: false, // 项目经历
    isEdu: false, // 教育经历
    isCertificate: false, // 资格证书
    isVideo: false, // 视频简历
    isAttachment: false, // 附件简历
  })

  useEffect(() => {
    if (!diyDetails.resumeUuid) {
      return
    }
    // 监听刷新事件
    if (isRefresh && props.refreshNum > 1) {
      $.showLoading('加载中...')
      onRefresh().finally(() => {
        $.hideLoading()
      })
    } else {
      setIsRefresh(true)
    }
  }, [props.refreshNum])

  useEffect(() => {
    initLoad()
  }, [])

  const initLoad = async () => {
    $.showLoading('加载中...')
    const details = getState().storage.myResumeDetails
    if (details && details.basicResp && details.basicResp.resumeUuid) {
      // 初始化处理
      formatResumeDetails(details).then((diyDetails: any) => {
        setDiyDetails(diyDetails)
      })
    }
    // 判断找活名片是否存在
    const exist = await resumeExistsHandler()
    if (!exist) {
      $.hideLoading()
      return
    }

    onRefresh(true).catch(err => {
      $.hideLoading()
      console.log('onRefresh', err)
    })
  }

  /**
  * @param isShowRule 是否处理显示规则
  */
  const onRefresh = async (isShowRule = false) => {
    const { resumeUuid } = getState().resume.resumeExist
    const resData = await getResumeDetails(resumeUuid)
    const diyDetails: any = await formatResumeDetails(resData)
    setDiyDetails(diyDetails)
    if (isShowRule) {
      const showDetail = await handlerShowDetail()
      setShowElem(showDetail)
    }
    $.taro.stopPullDownRefresh()
    $.hideLoading()
  }

  return (
    <V className={s.page}>
      <CustomNavbar isHome={false} title='我的在线简历' />

      {/* 个人信息 */}
      <UserInfo dataSource={diyDetails.userInfo} />

      {/* 求职状态 */}
      <JobStatus dataSource={diyDetails.jobStatus} onRefresh={() => onRefresh()} />

      {/* 个人优势 */}
      {(showElem.isIntroduce || diyDetails.basicInfo.introduce)
        && <Introduce onClick={() => setIsRefresh(false)} onRefresh={() => onRefresh()} dataSource={diyDetails.basicInfo} />
      }

      {/* 工作城市 */}
      {(showElem.isCity || diyDetails.basicInfo.hopeAreaCity.length)
        && <JobCity onClick={() => setIsRefresh(false)} onRefresh={() => onRefresh()} dataSource={diyDetails.basicInfo} />
      }

      {/* 求职期望 */}
      <JobResume onClick={() => setIsRefresh(true)} dataSource={diyDetails.subJob} />
    </V>
  )
}
