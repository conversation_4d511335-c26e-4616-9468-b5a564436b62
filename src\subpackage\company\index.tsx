import { useEffect } from 'react'
import { ScrollView, Text as T, View as V } from '@tarojs/components'
import Page from '@/components/Page'
import styles from './index.module.scss'
import CompanyBenefit from './components/CompanyBenefit'
import CompanyAddress from './components/CompanyAddress'
import CompanyIntroduce from './components/CompanyIntroduce'
import CompanyPhoto from './components/CompanyPhoto'
import useReducer from '@/hooks/useReducer'
import { FormatEnterpriseOptionListType, IntroduceList, processCompanyIntroduce, processEnterpriseOptionList, processProductIntroduce, processTalentDevelopment, processVideoImage, TalentDevelopmentType, VideoImageType } from './formatEnterpriseMainView'
import TalentDevelopment from './components/TalentDevelopment'
import ProductIntroduce from './components/ProductIntroduce'
import BusinessInformation from './components/BusinessInformation'
// import Header from '@/components/Header'

type Res = Models['POST/enterprise/v1/enterpriseHomepage/enterpriseMainView']['Res']['data']

type InitState = {
  /** * 企业基础信息 */
  baseInfo: Partial<Res['enterpriseBaseInfo']>
  /** 福利信息 */
  enterpriseOptionList: Partial<FormatEnterpriseOptionListType>
  /** 公司介绍的 一些信息 */
  introduceList: IntroduceList
  /** 图片和视频信息 */
  videoImage: Partial<VideoImageType>
  /** 人才发展 */
  talentDevelopment: TalentDevelopmentType
  /** 产品介绍 */
  productIntroduce: any[]
}

/**
 * 公司企业详情
*/
export default () => {
  // return null
  const sysInfo = $.sysInfo()
  // 1281
  const { infoId = 1281 } = $.router.query || {}

  const [{ baseInfo, enterpriseOptionList, introduceList, videoImage, talentDevelopment, productIntroduce }, dispatch] = useReducer<InitState>({
    baseInfo: {},
    enterpriseOptionList: {},
    introduceList: [],
    videoImage: {},
    talentDevelopment: [],
    productIntroduce: [],
  })

  const getData = async () => {
    $.taro.showLoading()
    try {
      const [data] = await $.request['POST/enterprise/v1/enterpriseHomepage/enterpriseMainView']({
        enterpriseBaseInfoId: infoId,
        tenantKey: 'YPZP',
        // wechat_token: "wxdcc7f8e70a22d31f"
      })

      // 处理 福利数据
      const d = processEnterpriseOptionList((data.enterpriseOptionList || []))
      // 处理 公司介绍数据
      const introduceList = processCompanyIntroduce(data)
      // 处理视频和图片
      const videoImage = processVideoImage(data)
      dispatch({
        baseInfo: data.enterpriseBaseInfo,
        enterpriseOptionList: d,
        introduceList,
        videoImage,
        talentDevelopment: processTalentDevelopment(d),
        productIntroduce: processProductIntroduce(data),
      })
    } catch ([, err]) {
      if (err.code !== 0) {
        $.router.back()
        console.log('-000')
      }
    } finally {
      $.taro.hideLoading()
    }
  }

  useEffect(() => {
    getData()
  }, [])

  return (
    <Page>
      <V className={styles.wrap} style={{ backgroundImage: baseInfo.enterpriseAvatar ? `url(${baseInfo.enterpriseAvatar})` : 'unset' }}>
        {/* <Header opacity={0} /> */}
        <ScrollView style={{ height: 'calc(100vh)' }} scrollY className={styles.scrollView}>
          <V className={styles.scrollContainer} style={{ paddingTop: `${sysInfo.headerTop}px` }}>
            {
              baseInfo.name && <CompanyBenefit data={baseInfo} enterpriseOptionList={enterpriseOptionList} />
            }
            {
              baseInfo.address && <CompanyAddress {...{ address: baseInfo.address, location: baseInfo.location } as Exclude<Res['enterpriseBaseInfo'], 'address' | 'location'>} />
            }
            { !!introduceList.length && <CompanyIntroduce data={introduceList} /> }
            {
              !!(videoImage.imageList?.length || videoImage.videoList?.length) && <CompanyPhoto infoId={infoId} data={videoImage as Required<VideoImageType>} />
            }
            {
              !!talentDevelopment.length && <TalentDevelopment data={talentDevelopment} />
            }
            {
              !!productIntroduce.length && <ProductIntroduce data={productIntroduce} />
            }
            {
              baseInfo.name && <BusinessInformation data={baseInfo} />
            }
          </V>
        </ScrollView>
      </V>
    </Page>
  )
}
