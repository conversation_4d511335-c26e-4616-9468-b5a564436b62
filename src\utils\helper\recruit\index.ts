/*
 * @Date: 2024-12-02 14:07:01
 * @Description: 招工相关
 */

/**
 * 岗位详情参数
 */
type JobDetailParams = {
  jobId: string,
  occIds: number[],
  sortTime: string,
  recommend: boolean
}
import { store } from '@/core/store'
import { haversineDistance } from '@/utils/location'

/** 获取招工详情数据 */
export const queryJobDetail = $.createPromiseCache((params: JobDetailParams) => {
  const newParams = {
    jobId: params.jobId,
    occIds: params.occIds || [],
    sortTime: $.isTimestampOrDate(decodeURIComponent(params.sortTime)),
    recommend: params.recommend,
  } as any

  return $.request['POST/job/v2/job/info']($.delNullOp(newParams), { hideMsg: true })
})

/** 融资规模 */
export const FINANCE_SCALE_MAP = [
  { label: '未融资', value: '1' },
  { label: '天使轮', value: '2' },
  { label: 'A轮', value: '3' },
  { label: 'B轮', value: '4' },
  { label: 'C轮', value: '5' },
  { label: 'D轮及以上', value: '6' },
  { label: '已上市', value: '7' },
  { label: '不需要融资', value: '8' },
]

/** 人员规模 */
export const STAFF_SIZE_MAP = [
  { label: '0-20人', value: '1' },
  { label: '20-99人', value: '2' },
  { label: '100-499人', value: '3' },
  { label: '500-999人', value: '4' },
  { label: '1000-9999人', value: '5' },
  { label: '10000人以上', value: '6' },
]

export const getLabelByValue = (labelValueMap, value) => {
  return (labelValueMap.find(item => item.value == value) || {}).label || ''
}

/**
 * @name 埋点上报
 * @param {string} eventName 事件名称
 * @param {Data} data 招工详情数据
 * @param {object} eventData 自定义事件数据
 */
export const uploadStatisticsData = async (eventName, info, eventData: any = {}) => {
  if (!info) {
    return
  }

  const { buriedData = {} } = $.router.data

  const statistics = {
    position_source: info.tenant == 'YPHT' ? 2 : 1,
    info_id: String(info.jobId),
    content_texts: info.detail,
    free_information: '免费',
    job_location: `${info.location.longitude},${info.location.latitude}`,
    /** 接通率 */
    label_id: '-99999',
    check_degree: info.checkDegreeStatus,
    label_texts: '-99999',
    consumption_product_score: 0,
    /** 详细地址 */
    detailed_address: info.address,
    /** 距离 */
    post_distance: getProjectAddressDistanceTxt(info.location),
    /** 岗位状态 */
    position_status: '-99999',
    duty_guarantee: '-99999',
    // 定价方案ID
    fix_price_id: '0',
    keywords_source: '',
    occupations_type: info.occMode == 2 ? '招聘' : '订单',
    is_famous_company: '-99999',
    recommend_reason: '-99999',
    search_result: '',
    dialing_interval_duration: '-99999',

    ...buriedData,
    ...info.buried_point_data,
    ...eventData,
  }

  $.report.event(eventName, statistics)
}

/**
 * @name 获取用户与项目地址之间的距离文本
 * @param {StringConstructor} location 项目经纬度
 * @return 获取格式化后的距离文本
 */
export function getProjectAddressDistanceTxt(projectLocation?: any): string {
  const { latitude: myLat, longitude: myLon } = store.getState().storage.userLocation || {}

  const { latitude, longitude } = projectLocation
  return haversineDistance(latitude, longitude, myLat, myLon)
}
