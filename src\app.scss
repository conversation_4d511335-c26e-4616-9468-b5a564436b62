
page {
  font-size: 30rpx;
  color: rgba(0, 0, 0, 0.85);
}

view, scroll-view, textarea, input, image, text, button, div {
  box-sizing: border-box;
}

/** 骨架屏 Start */
.skeleton {
  position: relative;
  overflow: hidden;

  &::after {
    content: ' ';
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #f2f2f2 linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
    background-size: 400% 100%;
    animation: skeleton-loading 1.4s ease infinite;
  }
}

.skeleton-bg {
  background: #f2f2f2 linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
  background-size: 400% 100%;
  animation: skeleton-loading 1.4s ease infinite;
}

@keyframes skeleton-loading {
  0% {
    background-position: 100% 50%;
  }
  to {
    background-position: 0 50%;
  }
}

.tab-bar-height {
  height: calc(112px + constant(safe-area-inset-bottom));
  height: calc(112px + env(safe-area-inset-bottom));
}
