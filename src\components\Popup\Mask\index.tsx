import classNames from 'classnames'
import s from './index.module.scss'

export type MaskProps = {
  /** 是否显示 */
  visible: boolean
  /** 渲染的内容 */
  children: React.ReactNode
  /** 关闭事件 */
  onClose?: () => void
  /** 点击遮罩是否触发关闭，默认true */
  maskClose?: boolean
  /** 层级 默认10000 */
  zIndex?: number
  /** 样式 */
  className?: string
  /** 行内样式 */
  style?: React.CSSProperties
  /** 禁止滑动 ，仅微信有效 */
  catchMove?: boolean
  /** 禁止滑动 */
  disableScroll?: boolean
}

/**
 * mask 蒙尘
*/
export default (props: MaskProps) => {
  const { children, visible, onClose, maskClose = true, zIndex = 10000, style, className, disableScroll = false, catchMove = true } = props

  const onClickMask = () => {
    if (maskClose) {
      onClose?.()
    }
  }
  return (
    <V
      className={classNames(s.mask, className)}
      style={{ visibility: visible ? 'visible' : 'hidden', opacity: visible ? 1 : 0, zIndex, ...style }}
      onClick={onClickMask}
      catchMove={catchMove}
      disableScroll={disableScroll}
    >
      {children}
    </V>
  )
}
