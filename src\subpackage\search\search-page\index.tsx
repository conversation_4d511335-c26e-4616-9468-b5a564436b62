import { useCallback, useEffect, useRef, useState } from 'react'
import { useDidShow } from '@tarojs/taro'
import SearchInput from '@/components/SearchInput'
import SearchContent from '../components/SearchContent'
import SearchLabel, { LabelItem } from '../components/SearchLabel'
import { useSelector } from '@/core/store'
import { saveSearchHistory, clearSearchHistory } from '../utils'
import s from './index.module.scss'
import CustomNavbar from '@/components/CustomNavbar'
import Page from '@/components/Page'

export default () => {
  const { origin = 'recruit' } = $.router.query || {}

  // 获取历史搜索记录
  const searchHistory = useSelector((state) => state.storage.searchHistory)[origin]
  const sysInfo = $.sysInfo()

  const [searchValue, setSearchValue] = useState($.router.query.keyword || '')
  const [searchLabelList, setSearchLabelList] = useState<LabelItem[]>([])
  const [showSearchLabel, setShowSearchLabel] = useState(false)

  const listRef = useRef([])

  useDidShow(() => {
    listRef.current = []
  })

  /** 获取标签列表 */
  const fetchLabelList = useCallback(
    $.debounce(async (value) => {
      const keyword = value.trim().replace(/\n/g, '')
      if (keyword) {
        const [data] = await $.request['POST/labelService/v1/occLabel/searchRecommendedLabelList']({ keyword })
        // 构造正则表达式并替换关键词为高亮
        const regKeywords = new RegExp(keyword, 'gi')
        const repKeywords = '<span style=\'color:#0092ff\'>$&</span>'

        listRef.current = data.list || []

        // 生成标签列表
        const labelList = (Array.isArray(data?.list) ? data.list : []).map((item) => ({
          ...item,
          aliasName: `<div>${item.label.replace(regKeywords, repKeywords)}</div>`,
        }))

        setSearchLabelList(labelList)
      }
    }, 150),
    [],
  )

  /** 搜索输入值变更 */
  const changeSearchValue = (value: string, showLabel = true, toSearch = false, isHistory = false) => {
    console.log('changeSearchValuevalue:', value)
    setSearchValue(value)

    if (showLabel) {
      fetchLabelList(value) // 获取标签列表
      setShowSearchLabel(!!value) // 更新显示状态
    }

    if (toSearch) {
      onSearch(value, isHistory ? '4' : '1')
    }
  }

  /** 清空搜索输入框 */
  const onClear = () => {
    setSearchValue('')
    setShowSearchLabel(false)
  }

  /** 触发搜索 */
  const onSearch = (value: string, keywords_source = '5') => {
    const trimValue = value.trim()
    if (!trimValue) {
      $.msg('搜索内容不能为空')
      return
    }
    if (origin === 'recruit') {
      $.report.event('searchInput', {
        keywords: value,
        search_source_id: '2',
        // search_source: '职位搜索中间页顶部搜索框',
      })
    }
    saveSearchHistory(origin, trimValue) // 保存历史搜索
    $.report.event('searchResultPageExposure', {
      request_id: '',
      enter_id: '1',
      source_id: '1',
      keywords: value,
      gs_keywords_show: [],
      gs_keywords_selected: [],
    })
    $.router.push('/subpackage/search/search-result/index', { keyword: value, keywords_source })
  }

  /** 清空历史搜索 */
  const onClearSearchContent = () => {
    clearSearchHistory(origin)
  }

  useDidShow(() => {
    setShowSearchLabel(false)
  })
  return (
    <Page backgroundColor="#fff">
      <CustomNavbar isHome={false} title='搜索职位' />
      <V className={s.container}>
        <SearchInput
          placeholder="搜索您想找的职位"
          value={searchValue}
          onInput={changeSearchValue}
          onClear={onClear}
          onSearch={onSearch}
        />
        <SearchContent
          list={searchHistory}
          title="历史搜索"
          onClear={onClearSearchContent}
          onClick={(value) => changeSearchValue(value, false, true, true)}
        />
        {showSearchLabel && (
          <SearchLabel
            list={searchLabelList}
            // top={sysInfo}
            top={`${sysInfo.statusBarHeight + sysInfo.headerHeight + 50}px`}
            onClick={(value) => changeSearchValue(value, false, true, false)}
          />
        )}
      </V>
    </Page>
  )
}
