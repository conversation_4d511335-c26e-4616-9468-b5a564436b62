import { memo } from 'react'
import s from './index.module.scss'
import IconFont from '@/components/IconFont'
import { actions, dispatch, useSelector } from '@/core/store'
import { getLocation } from '@/utils/location'

type Props = {
  /** 页面ui显示层级 */
  levelUi: number
  /** change事件 */
  onChange: (val: any) => void
}
const Index = memo((props: Props) => {
  const userLocation = useSelector((state) => state.storage.userLocation)

  const onLocation = () => {
    getLocation().then(({ city, province, latitude, longitude }) => {
      const value = city || province
      const isValue = !!(value && value.id)

      if (isValue && props.levelUi == 2) {
        value.isFull = true
        if (!city) {
          value.level = 2
        }
      } else if (isValue && props.levelUi == 3) {
        value.isFull = true
        if (city) {
          value.level = 3
        } else if (province) {
          value.level = 2
        }
      }
      if (isValue) {
        dispatch(actions.storage.setItem({ key: 'homeLocation',
          value: {
            success: true,
            data: {
              latitude: String(latitude),
              longitude: String(longitude),
              areaId: value?.id,
              name: value?.name,
            },
          } }))
        props.onChange({ value })
      } else {
        $.msg('定位失败，请稍后再试')
      }
    }).catch((err) => {
      if (!err || err.code != 401) {
        $.msg('定位失败，请稍后再试')
      }
    })
  }

  const onSelect = () => {

  }

  return (<V className={s.header}>
    <V className={s['left-icon']}>
      <IconFont type="yp-dingwei" size={34} />
    </V>
    <V className={s.left}>
      <V className={s.text}>
        {userLocation.success && (userLocation.city || userLocation.province)
          ? <V>
            当前位置：
            <T onClick={onSelect} className={s.value}>{
              userLocation.city ? userLocation.city.name : userLocation.province.name
            }</T>
          </V>
          : <T className={s.value}>定位服务未授权</T>
        }
      </V>
    </V>
    <V className={s.right} onClick={() => onLocation()}>
      <V className={s['right-icon']}>
        <IconFont type="yp-zhibiao" size={38} />
      </V>
      <V className={s['right-text']}>重新定位</V>
    </V>
  </V>
  )
})

export default Index
