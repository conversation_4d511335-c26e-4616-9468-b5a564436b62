import {
  useDidHide,
  useDidShow,
  useLoad,
  usePullDownRefresh,
  useReachBottom,
} from '@tarojs/taro'
import { useEffect, useRef, useState } from 'react'
import Page from '@/components/Page'
import s from './index.module.scss'
import SearchInput from '@/components/SearchInput'
import Click from '@/components/Click'
import { IClassifyDataRp } from '@/core/utils/index.d'
import ResultFilter from '../components/ResultFilter'
import List from '@/pages/index/components/List'
import useRefValue from '@/hooks/useRefValue'
import useStates from '@/hooks/useStates'
import { actions, dispatch, store } from '@/core/store'
import useHomeLocation from '@/hooks/useHomeLocation'

type ParamsType = { keyword: string };

export default () => {
  const sysInfo = $.sysInfo() || {}

  const [keyword, setKeyword] = useState('') // 搜索关键字

  const [selectedJobs, setSelectedJobs] = useState<Array<IClassifyDataRp>>([]) // 职位状态

  const [list, setList] = useState<any[]>([])

  const [result, setResult] = useStates({
    loadingStatus: undefined as any, // 'more' | 'done'
    skeleton: true, // 骨架屏显示
    currentPage: 1,
  })

  const { currentPage, loadingStatus, skeleton } = result

  // 搜索框点击
  const onClickSearch = () => {
    if (getCurrentPages().length > 1) {
      $.router.back()
    } else {
      $.router.replace('/subpackage/search/search-page/index', { keyword })
    }
  }

  /** 获取定位 */
  const homeLocation = useHomeLocation((nextHomeLocation) => {
    onRefResh({ page: 1 }, { nextHomeLocation })
  })

  const oldHomeLocation = useRef(homeLocation.data.areaId)

  useDidShow(() => {
    if (homeLocation.data.areaId !== oldHomeLocation.current) {
      onRefResh({ page: 1 })
    }
  })

  useDidHide(() => {
    oldHomeLocation.current = homeLocation.data.areaId
  })

  // 城市选择回调
  const handleCitySelect = (city: ILocation.TAreaData) => {
    const { success, data } = store.getState().storage.homeLocation
    const nextHomeValue = {
      success,
      data: {
        ...(data || {}),
        areaId: Number(city.id || 1),
        name: city.name,
      },
    }
    dispatch(
      actions.storage.setItem({ key: 'homeLocation', value: nextHomeValue }),
    )
  }

  // 职位选择回调
  const handleJobSelect = (jobs: Array<IClassifyDataRp>) => {
    setSelectedJobs(jobs)
  }

  // 为了防止卡片重复渲染，这里用 useRefValue
  const onClickCard = useRefValue((info) => {
    const query = {
      jobId: info.jobId,
      pagination: info.currentPage,
      sortTime: info.sortTime,
      recommend: false,
      topping: info.isTop ? 1 : 0,
      pagination_location: info.pagination_location,
      source_id: 2,
      source: '搜索结果',
      occIds: selectedJobs.map((item) => Number(item.id)),
    }
    $.router.push('/pages/recruit/detail', query, {
      buriedData: info.buriedData,
    })
  })

  const onRefResh = async ({ page = currentPage }, {
    nextHomeLocation = homeLocation,
    keywords = keyword,
  } = {}) => {
    try {
      // 骨架屏
      if (page === 1) {
        $.taro.pageScrollTo({ scrollTop: 0 })
        setList([])
        setResult({ skeleton: true })
      } else if (page !== 1 && loadingStatus !== 'more') {
        setResult({ loadingStatus: 'more' })
      }

      const occV2 = getOcc(selectedJobs)
      const locationValues = getLocation(nextHomeLocation)

      const [data] = await $.request['POST/job/v2/search/job/search']({
        keywords,
        tabAreaId: homeLocation.data?.areaId,
        areaId: homeLocation.data?.areaId,
        occV2,
        pageSize: 15,
        currentPage: page,
        useIpLocation: false,
        ...locationValues,
      })
      const dataList = data.list.map((item: any, index) => {
        item = { ...item, pagination_location: index + 1, currentPage: page }
        return item
      })
      let newList = page === 1 ? dataList : list.concat(dataList)
      const { keywords_source } = $.router.query
      newList = newList.map((item, index) => {
        item = {
          ...item,
          pagination_location: index + 1,
          currentPage: page,
          buriedData: {
            ...item.buriedData,
            pagination: String(page),
            pagination_location: item.pagination_location,
            type_options: '',
            job_options: '',
            sort_time: item.sortTime,
            search_result: '有结果',
            location_id: String(index + 1),
            keywords_source,
            source_id: '2',
            topping: item.isTop ? '1' : '0',
            source: '搜索结果',
          },
        }
        return item
      })

      setList(newList)
      setResult({
        skeleton: false, // 骨架屏显示
        currentPage: page,
        loadingStatus: data.list.length == 15 ? 'more' : 'done',
      })
    } catch {
      setResult({ loadingStatus: 'done', skeleton: false })
    }
  }

  useLoad((params: ParamsType) => {
    setKeyword(params.keyword) // 搜索关键字
  })

  // 监听城市和职位变化
  useEffect(() => {
    if (!keyword) return

    onRefResh({ page: 1 }, { keywords: keyword })
  }, [homeLocation.data, selectedJobs, keyword])

  useReachBottom(() => {
    onRefResh({ page: currentPage + 1 })
  })

  usePullDownRefresh(async () => {
    await onRefResh({ page: 1 })
    $.taro.stopPullDownRefresh()
  })

  const isLandingPage = getCurrentPages().length <= 1
  const paddingLeft = sysInfo.leftBackRect?.homeButtonIcon?.right && isLandingPage
    ? sysInfo.leftBackRect.homeButtonIcon.right + 6
    : (sysInfo.leftBackRect?.backButtonInteractive.right || 0) + 12

  return (
    <Page>
      <V className={s.topBox}>
        <V
          className={s.header}
          style={{
            paddingTop: sysInfo.statusBarHeight,
            height: sysInfo.headerHeight + sysInfo.statusBarHeight,
          }}
        >
          <V
            className={s.search}
            style={{
              height: sysInfo.menuRect.height,
              width: sysInfo.menuRectLeft - 6,
              paddingLeft,
            }}
          >
            <Click onClick={onClickSearch} className={s.searchBox}>
              <SearchInput rightSlot={<V></V>} value={keyword} disabled />
            </Click>
          </V>
        </V>
        <ResultFilter
          city={homeLocation.data} // 传递城市值
          jobs={selectedJobs} // 传递职位值
          onCitySelect={handleCitySelect} // 城市选择回调
          onJobSelect={handleJobSelect}
        />
      </V>

      <V className={s.container} id="listbody">
        <List
          topHeight="270px"
          noDataText="没有找到您想要的内容,您可以换个内容搜索"
          onClickCard={onClickCard}
          location={homeLocation}
          homeNearbyLocation={{}}
          loadingStatus={loadingStatus}
          skeleton={skeleton}
          list={list}
          currentPage={currentPage}
          listType={-1}
        />
      </V>
    </Page>
  )
}

/**
 * 获取职业分类数据
 * @param jobs 职业数据数组
 * @returns 包含行业信息和类型信息的对象
 *
 */
const getOcc = (jobs: IClassifyDataRp[]) => {
  if (!Array.isArray(jobs) || !jobs.length) return [{ industry: -1, occIds: [] }]
  const occV2 = jobs.map((item) => ({
    industry: Number(item?.industries?.[0]),
    occIds: [Number(item.id)],
  }))
  return occV2
}

/* 根据homeLocation参数获取位置信息 */
export function getLocation(homeLocation) {
  return {
    location: {
      latitude: homeLocation?.data?.latitude,
      longitude: homeLocation?.data?.longitude,
    },
  }
}
