/*
 * @Date: 2024-11-23 10:06:55
 * @Description: 列表
 */

import { View as V, Text as T, Image as Img } from '@tarojs/components'
import Skeleton from '../Skeleton'
import JobCard from '@/components/JobCard'
import Loading from '@/components/Loading'
import s from './index.module.scss'

export default ({ onClickCard, location, homeNearbyLocation, loadingStatus, list, skeleton, listType, noDataText = '暂无相关职位', topHeight = 0 }) => {
  if (skeleton) {
    return <Skeleton />
  }

  const nodata = list.length === 0 && loadingStatus === 'done' ? (
    <V className={s.noData} style={{ height: topHeight ? `calc(100vh - ${topHeight})` : 'initial' }}>
      <Img className={s.noDataImg} src='https://cdn.yupaowang.com/yupao_mini/alipay/home_no_data.png' />
      <T className={s.noDataText}>{noDataText}</T>
    </V>
  ) : null

  return (
    <V>
      <V>
        {list.map((item, index) => {
          return <JobCard onClickCard={onClickCard} location={location} info={item} key={`${index}-${item.jobId}`} index={index} />
        })}
      </V>
      {/* 定位失败 */}
      {listType === 3 && homeNearbyLocation.success === false ? (
        <V className={s.noData}>
          <Img className={s.noDataImg} src='https://cdn.yupaowang.com/yupao_mini/alipay/home_no_data.png' />
          <T className={s.noDataText}>暂无符合职位，更换筛选范围试试</T>
        </V>
      ) : nodata}
      {/* 加载状态 */}
      <V className={s.loading}>
        {loadingStatus === 'more' ? (
          <>
            <Loading />
            <T className={s.loadingText}>加载中...</T>
          </>
        ) : null}
        {/* 无内容状态 */}
        {loadingStatus === 'done' && list.length > 0 ? (
          <T className={s.loadingTextDone}>- 没有更多内容了 -</T>
        ) : null}
      </V>
    </V>
  )
}
