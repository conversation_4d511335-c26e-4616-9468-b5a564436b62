import { useEffect, useRef, useState } from 'react'
import classNames from 'classnames'
import style from './index.module.scss'
import { store, useSelector } from '@/core/store/index'
import ItemView from './components/ItemView'
import ResumeFooter from './components/ResumeFooter'
import { judgeJumpPage, resultProcessing, validationForm } from './utils'
import { handlePublishResult, judgePublishLogic, savePublishData } from '@/core/utils/publish'
import { IClassifyDataRp } from '@/core/utils/index.d'
import storage from '@/store/storage/storage'
import { getAreaSearches } from '@/utils/location'
import CustomNavbar from '@/components/CustomNavbar'
import { publishResumeResultReport } from '../../utils'

type IResumePublishProps = {
  value?: {
    publishRuleSwitch?: boolean
    hopeAreas?: Array<string>
    occupations?: Array<any>
  }
  isPdBtm?: boolean
  // 发布成功后直接返回上一页，不跳转到首页
  isBack?: boolean

   // 是否是 弹窗样式 (默认false)
   isPopStyle?: boolean
   // 城市标题
   cityLabel?: string
   // 工种标题
   occLabel?: string
   // 城市placeholder
   cityPlaceholder?: string
   // 工种placeholder
   occPlaceholder?: string
   // 关闭弹窗
   onHidePop?: () => void
   // 点击授权定位
   onClickGps?: () => void
   // 隐藏定位提示模块 (默认不隐藏)
   isShowGpsTips?: boolean
   // 发布成功后- 回调事件
   onConfirmCbFn?: () => void
}

export default function ResumePublish(props: IResumePublishProps) {
  const { value = {}, isPdBtm = false, isBack = false, isPopStyle = false, cityLabel = '您理想的工作城市是', occLabel = '您的期望职位是', cityPlaceholder = '请选择期望工作城市', occPlaceholder = '请选择期望职位', onHidePop, onClickGps = () => {}, isShowGpsTips = true, onConfirmCbFn } = props
  const { publishRuleSwitch, hopeAreas = [], occupations = [] } = value || {}

  const [hopeAreaObj, setHopeAreaObj] = useState<{ hopeAreas: string[], hopeAreaStr: string }>({ hopeAreas: [], hopeAreaStr: '' }) // 工作期望城市
  const [occObj, setOccObj] = useState<{ occupations: Array<IClassifyDataRp>, occsStr: string }>({ occupations: [], occsStr: '' }) // 职位
  const occChange = useRef(true)

  // 发布完善流程
  const [publishData, setPublishData] = useState({
    /** 底部按钮文案 */
    subBtnTxt: '发布简历',
    // 发布完善流程
    publishFlow: {
      // 第几步发布  1.快速发布页面 2.完善流程一 3.完善流程二
      subStep: 1,
      // 完善流程一和流程二是否需要跳转  数组中有数字 1:跳转 0:不跳转 下标0对应完善流程一，以此类推
      perfects: [1, 1],
    },
    // 模板,取第一个工种的模板
    template: {},
    // 用户信息
    userInfo: {},
  })

  const classifyConfig = useSelector((state) => state.classify.classifyConfig)
  const { basicConfigResp } = useSelector(state => state.resume.globalConfig)

  useEffect(() => {
    initHopeCity()
  }, [hopeAreas])

  useEffect(() => {
    if (occChange.current) {
      initOccs()
    } else {
      occChange.current = true
    }
  }, [occupations])

  const initHopeCity = async () => {
    const pubishData = storage.getItemSync('pubishData')
    const { hopeAreas: storeHopeAreas } = pubishData || {}
    const { level, pid, id } = store.getState().storage.userLocation
    const areaId = level == 3 ? pid : id
    let nHopeAreas: Array<string> = []
    const nStoreHopeAreas = storeHopeAreas.filter(Boolean)
    if ($.isArrayVal(nStoreHopeAreas)) {
      nHopeAreas = [...nStoreHopeAreas]
    } else if ($.isArrayVal(hopeAreas)) {
      nHopeAreas = [...(hopeAreas)]
    } else if (areaId) {
      nHopeAreas = [`${areaId}`]
    }
    const hopeAreaStr = (await getAreaSearches(nHopeAreas, 'id'))
      .filter(Boolean)
      .map(({ current }) => current && current.name)
      .filter(Boolean).join('、')
    setHopeAreaObj({ hopeAreas: nHopeAreas, hopeAreaStr })
  }

  const initOccs = async () => {
    const pubishData = storage.getItemSync('pubishData')
    const { occs } = pubishData || {}
    let nOccupations: Array<IClassifyDataRp> = []
    if ($.isArrayVal(occs)) {
      nOccupations = [...occs]
    } else if ($.isArrayVal(occupations)) {
      const ids = occupations.map(occ => occ.occId)
      nOccupations = await $.getClassifyOfIndByIds(ids)
      nOccupations = nOccupations.map(occ => {
        const nocc = { ...occ }
        const oocc = occupations.find(occ2 => occ2.occId === occ.id)
        if ($.isObjVal(oocc) && $.isArrayVal(oocc.industries)) {
          nocc.industries = oocc.industries
        }
        return nocc
      })
    }
    const maxSelectNum = classifyConfig.recruitWorkerSearchFindJobPublishOccCnt || 5
    setOccObj({ occupations: nOccupations.slice(0, maxSelectNum), occsStr: nOccupations.map(occ => occ.name).join('、') })
    onOccChange({ detail: { value: nOccupations, rqTpe: 'def' } })
  }

  const onCityCLick = async () => {
    const { maxCityNum } = basicConfigResp || {}
    let hopeAreaStr = '选择城市'
    if (hopeAreaObj.hopeAreas) {
      hopeAreaStr = (await getAreaSearches(hopeAreaObj.hopeAreas, 'id')).filter(Boolean)
        .map(({ current }) => current && current.name)
        .filter(Boolean).join('、')
    }

    $.openAddress({
      areas: hopeAreaObj.hopeAreas,
      level: 2,
      title: '选择城市',
      maxNum: maxCityNum,
      // disabledIds: [33, 34, 35],
      hideNation: true,
    }, {
      source: '发布找活名片',
      source_id: '10',
      button_name: hopeAreaStr,
    }, (data) => {
      if ($.isArrayVal(data)) {
        const nHopeAreas = data.map(item => `${item.id}`)
        setHopeAreaObj({ hopeAreas: nHopeAreas, hopeAreaStr: data.map(item => `${item.name}`).join('、') })
        savePublishData('hopeAreas', nHopeAreas)
      }
    })
  }

  const onClassifyClick = () => {
    $.openClassify({
      value: occObj.occupations,
      maxSelectNum: classifyConfig.recruitWorkerSearchFindJobPublishOccCnt || 5,
      source_id: 8,
    }, (data) => {
      const occsStr = data.map((occ: any) => (occ.name || '')).filter(name => name).join('、')
      occChange.current = false
      setOccObj({ occupations: data, occsStr })
      if (!isPopStyle) {
        onOccChange({ detail: { value: data } })
      }
    })
  }

  const onOccChange = async (e) => {
    const { value, rqTpe } = e.detail
    let sData: any = { subBtnTxt: '发布简历', publishFlow: { subStep: 1, perfects: [1, 1] } }
    if (value.length) {
      if (rqTpe != 'def') {
        savePublishData('occs', value)
        savePublishData('positionType', 1)
        savePublishData('salaryObj', {})
        savePublishData('preferenceList', [])
      }
      const occIds = value.map(item => Number(item.id))
      const [data] = await $.request['POST/resume/v3/template/findWithUser']({ occIds })
      const { templates, userInfo } = data || {}
      const template = $.isArrayVal(templates) ? { ...templates[0] } : {}
      const publishFlow = judgePublishLogic(template, userInfo)
      sData = {
        subBtnTxt: publishFlow.subStep == 1 ? '发布简历' : '下一步',
        publishFlow,
        template,
        userInfo,
      }
    }
    setPublishData((prev) => ({ ...prev, ...sData }))
  }

  const onConfirm = async () => {
    /** 按钮点击--弹窗-埋点 */
    const page = $.router.getCurrentPage()
    if (page.$taroPath.indexOf('pages/index/index') >= 0 && isPopStyle) {
      const reportData = {
        name: '简历发布前置弹窗',
        page_name: '首页',
        click: publishData.subBtnTxt,
      }
      $.report.event('userPopupClick', reportData)
    }
    const occAreaReq: any = {
      occupations: occObj.occupations.map((occ) => ({
        occupation: occ.id,
        name: occ.name,
        mode: occ.mode,
        industries: occ.industries,
      })),
      hopeArea: hopeAreaObj.hopeAreas,
    }
    if (!validationForm(occAreaReq)) {
      return
    }
    $.showLoading('请求中...')
    const params: any = { occAreaReq }
    const { publishFlow, template, userInfo } = publishData
    console.log('publishFlow:', publishFlow)

    if (publishFlow.subStep != 1) {
      const isJudge = judgeJumpPage({ publishFlow, template, userInfo, params, publishRuleSwitch, isBack, isPopStyle })
      if (isJudge) return
    }
    storage.removeSync('pubishData')
    $.request['POST/resume/v3/publish/publish']({ ...params }, { hideMsg: true }).then(() => {
      $.hideLoading()
      resultProcessing({ publishFlow, template, userInfo, publishRuleSwitch, isBack, isPopStyle }, params, { publishSuccess: onConfirmCbFn })
    }).catch((res) => {
      $.hideLoading()
      handlePublishResult(res[1] || {}, '发布')
      publishResumeResultReport('0', '0', occAreaReq, { published_source_id: isPopStyle ? '1' : '' })
    })
  }

  return (
    <V className={style.page}>
      {!isPopStyle && <CustomNavbar isBack title=' ' />}
      <V className={style.content}>
        <V className={classNames(style.item, isPopStyle && style.pop_item)}>
          <T className={classNames(style.title, isPopStyle && style.pop_title)}>想找什么工作？</T>
          {isPopStyle && <Img onClick={onHidePop} className={style.closeImg} src="https://cdn.yupaowang.com/yupao_mini/dy_Close-small3x.png" />}
        </V>
        <ItemView value={hopeAreaObj.hopeAreaStr} onClick={onCityCLick} title={cityLabel} placeholder={cityPlaceholder} desc={isPopStyle ? '' : '【可多选】'} />

        {/* 发布简历弹窗-手动定位 */}
        {isPopStyle && isShowGpsTips
        && <V className={style.addressTips} onClick={onClickGps}>
          <Img className={style.gpsImg} src="https://cdn.yupaowang.com/yupao_mini/Local_done_dy3x.png" />
          <V className={style.gpsText}>使用当前定位城市</V>
        </V>
        }

        <ItemView value={occObj.occsStr} onClick={onClassifyClick} title={occLabel} placeholder={occPlaceholder} desc={isPopStyle ? '' : '【可多选】'} />

      </V>
      {isPopStyle
        ? <V className={`${style.footer} ${($.isIos()) ? style.footer_ios : ''}`}>
          <V onClick={onConfirm} className={classNames(style.pop_btn)}><T className={style.pop_btnTxt}>{publishData.subBtnTxt}</T></V>
        </V>
        : <ResumeFooter isPdBtm={isPdBtm} publishRuleSwitch={publishRuleSwitch} btnTxt={publishData.subBtnTxt} onConfirm={onConfirm} />
      }
    </V>
  )
}
