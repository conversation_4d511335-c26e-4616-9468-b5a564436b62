/*
 * @Date: 2024-10-14 17:20:06
 * @Description: 路由
 */

import Taro from '@tarojs/taro'
import { stringify, parse } from 'qs'
import { wait } from '../utils'

export const tabBarList = [
  {
    pagePath: 'pages/index/index',
    path: '/pages/index/index',
    text: '首页',
  },
  {
    pagePath: 'pages/resume/index',
    path: '/pages/resume/index',
    text: '简历',
  },
  {
    pagePath: 'pages/mine/index',
    path: '/pages/mine/index',
    text: '我的',
  },
]

const isTabBar = (path) => {
  return !!tabBarList.find((item) => item.path === path)
}

/** 存放方法和数据 */
const content = {}

/** 存放tabbar的数据 */
const tabbarContent = {}

/** 获取路由上个页面带过来的数据 */
const getData = () => {
  const path = `/${getPath()}`
  if (isTabBar(path)) {
    return tabbarContent[path]?.data
  }
  const { navKey } = getCurrentPage().$taroParams
  return content[navKey]?.data || {}
}

/** 清除data */
const clearData = () => {
  const path = `/${getPath()}`
  tabbarContent[path] = undefined
}

/** 调用上一个页面跳转的方法 */
const event = async (data, wait = true) => {
  const { navKey } = getCurrentPage().$taroParams
  if (!content[navKey]) {
    return
  }
  const { path, event } = content[navKey]
  // 这个地方等待页面回来后才执行，避免在下个页面的时候，获取 getCurrentPage 的时候获取到了其他页面
  wait && (await awaitRender(path))
  event(data)
}

/** 等待当前页面渲染才会放行 */
const awaitRender = async (path, timeout = 5000) => {
  const start = Date.now()
  return new Promise(async (resolve, reject) => {
    while (true) {
      const p = getPath()
      if (p === path) {
        resolve(undefined)
        break
      }
      if (Date.now() - start > timeout) {
        reject(new Error('等待超时'))
        break
      }
      await wait(100) // 等待 100 毫秒
    }
  })
}

/** 获取当前页面 */
const getCurrentPage = () => {
  const pages = Taro.getCurrentPages()
  const len = pages.length
  return pages[len - 1]
}

const reLaunch = (path: string, query?: Record<string, any>, data?: any) => {
  if (isTabBar(path)) {
    // 存放数据
    tabbarContent[path] = { data }
    Taro.switchTab({ url: path })
    return
  }
  const navKey = Date.now()
  const q = { navKey, ...query }
  content[navKey] = { data, event, path: getPath() }
  Taro.reLaunch({ url: `${path}?${stringify(q)}` })
}

const push = (path: string, query: Record<string, any> = {}, data?: any, event?: Function) => {
  if (isTabBar(path)) {
    // 存放数据
    tabbarContent[path] = { data }
    Taro.switchTab({ url: path })
    return
  }
  const navKey = Date.now()
  const q = { navKey, ...query }
  content[navKey] = { data, event, path: getPath() }
  Taro.navigateTo({ url: `${path}?${stringify(q)}` })
}

const replace = (path: string, query: Record<string, any> = {}, data?: any, event?: Function) => {
  if (isTabBar(path)) {
    // 存放数据
    tabbarContent[path] = { data }
    Taro.switchTab({ url: path })
    return
  }
  const navKey = Date.now()
  const q = { navKey, ...query }
  content[navKey] = { data, event, path: getPath() }
  Taro.redirectTo({
    url: `${path}?${stringify(q)}`,
  })
}

const getCurrentPages = () => {
  return Taro.getCurrentPages()
}

const back = (delta = 1) => {
  Taro.navigateBack({ delta })
}

/** 获取路由参数 */
const getQuery = () => {
  // const { $taroTimestamp, navKey, ...options } = getCurrentPage().$taroParams
  // return parse(options)
  const { $taroTimestamp, navKey, ...options } = getCurrentPage().$taroParams
  const qsString = Object.entries(options).map(([k, v]) => `${decodeURIComponent(k)}=${decodeURIComponent(v)}`).join('&')
  const params = parse(qsString)
  return params
}

const getPath = () => {
  return getCurrentPage().route as string
}

const router = {
  push,
  replace,
  back,
  reLaunch,
  event,
  getCurrentPage,
  getCurrentPages,
  awaitRender,
  clearData,
  get path() {
    return getPath()
  },
  get data() {
    return getData()
  },
  get query() {
    return getQuery()
  },
}

export default router
