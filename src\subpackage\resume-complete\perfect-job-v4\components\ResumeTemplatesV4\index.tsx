import { Block } from '@tarojs/components'
import { useEffect, useRef } from 'react'
import InputWidget from '../InputWidget'
import FormCheckbox from '../FormCheckbox'

type IResumeTemplatesV4Props = {
  /** 控件列表 [{status: 是否隐藏控件 1.显示}] */
  controlList?: Array<any>,
  /** 控件对象 */
  controlObj?: any,
  mustKey?: string
  change?: (e: any) => void
}

// 输入框类型
const typeCodes = ['INPUT_STRING', 'INPUT_NUMBER', 'INPUT_WITH_UNIT']
/** 期望职位 */
const ResumeTemplatesV4 = (props: IResumeTemplatesV4Props) => {
  const { controlList = [], controlObj = {}, mustKey = 'ifManageMust', change } = props

  const list = useRef({})

  useEffect(() => {
    if ($.isArrayVal(controlList)) {
      const nList = {}
      controlList.forEach((code, index) => {
        nList[index] = controlObj[code]
      })
      list.current = nList
      change && change({ list: nList })
    }
  }, [controlList])

  const onChange = (value, index) => {
    const nList = { ...(list.current) }
    nList[index] = value
    list.current = nList
    change && change({ list: nList })
  }

  return (
    <Block>
      {
        (controlList || []).map((code, index) => {
          const control = controlObj[code]
          const { controlTypeCode } = control
          if (typeCodes.includes(controlTypeCode)) {
            return (
              <InputWidget
                key={code}
                control={control}
                mustKey={mustKey}
                change={(value) => onChange(value, index)}
              />
            )
          }
          return (
            <FormCheckbox
              key={code}
              control={control}
              mustKey={mustKey}
              change={(value) => onChange(value, index)}
            />

          )
        })
      }
    </Block>
  )
}

export default ResumeTemplatesV4
