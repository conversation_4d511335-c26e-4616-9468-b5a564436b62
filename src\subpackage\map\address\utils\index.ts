import type { TAddrTreeData, TTreeFullVal, TTreeState, TTreeVal } from './index.d'

import { getTreeData, getAreaSearches } from '@/utils/location'
import { addrConfDef, getAreaConfig, handlerData } from './tools'

const areaTreeFull = {
  /** 所有省份数组 */
  province: [] as TTreeFullVal,
  /** 所有城市，key值用省id作为key */
  city: {},
  /** 所有区域，key值用城市id作为key */
  district: {},
}

/** 页面地址配置 */
let addrConf = { ...addrConfDef }

/** 清空地址数据 */
function clearAreaTreeFull() {
  Object.assign(areaTreeFull, {
    province: [],
    city: {},
    district: {},
  })
}

/** 获取当前地址省市区id */
export function getAreaId(area: TTreeVal): {
  provinceId: string | number
  cityId: string | number
  districtId: string | number
} {
  const { level, isFull, id, pid, gid } = area
  if (level == 1) {
    return { provinceId: id, cityId: '', districtId: '' }
  }
  if (level == 2) {
    return isFull
      ? { provinceId: id, cityId: '', districtId: '' }
      : { provinceId: pid, cityId: id, districtId: '' }
  }

  return isFull
    ? { provinceId: pid, cityId: id, districtId: '' }
    : { provinceId: gid as number, cityId: pid, districtId: id }
}

/** 设置全部地区数据 */
async function setAreaTreeFull() {
  const cityIds = Object.keys(areaTreeFull.city || {})
  if ($.isArrayVal(areaTreeFull.province, 30) && $.isArrayVal(cityIds, 30)) {
    return areaTreeFull
  }
  clearAreaTreeFull()
  let treeData = await getTreeData()
  treeData = $.deepClone(treeData) as any
  treeData.forEach(({ children: listC, ...itemP }) => {
    /** 存储省份数据 */
    if ($.isArrayVal(areaTreeFull.province)) {
      areaTreeFull.province.push(itemP)
    } else {
      areaTreeFull.province = [itemP]
    }

    /** 二级地址 */
    if (itemP.id != '1') {
      areaTreeFull.city[itemP.id] = [
        { ...itemP, isFull: true, level: 2 },
      ]
    }
    if (listC && $.isArrayVal(listC, 1)) {
      listC.forEach(({ children: listD, ...itemC }) => {
        itemC = { ...itemC }
        /** 存储城市数据 */
        areaTreeFull.city[itemP.id].push(itemC)

        if (listD && $.isArrayVal(listD, 1)) {
          /** 三级地址 */
          areaTreeFull.district[itemC.id] = [{ ...itemC, isFull: true, level: 3 }]
          /** 存储城市区域数据 */
          listD.forEach((itemD) => {
            areaTreeFull.district[itemC.id].push({ ...itemD, gid: itemP.id })
          })
        }
      })
    }
  })

  return areaTreeFull
}

/** 设置选中状态 */
function setSelect(areaTree: TTreeFullVal, areaId: string | number) {
  const newAreaTree = $.deepClone(areaTree) || []
  /** 设置选中状态 */
  const areaFind = newAreaTree.find(item => item.id == areaId)
  if (areaFind) {
    areaFind.checked = true
  }
  return newAreaTree
}

/** 获取地址页的配置数据 */
export function getAreaConf() {
  const conf = getAreaConfig()
  addrConf = conf
  return getAreaConfig()
}

/** 通过地址id获取地址信息 */
async function getSelectSearches(areas: (string | number)[] = []) {
  const areaInfos = await getAreaSearches(areas)
  const selectAddrs: TTreeFullVal = []
  if (!$.isArrayVal(areaInfos)) {
    return { selectAddrs, areaInfos }
  }

  areaInfos.forEach(item => {
    const current = item.current ? { ...item.current } : ''
    let isFull = false
    if (current) {
      isFull = current.level == 1
      if (addrConf.level == 3 && current.level == 2 && $.isArrayVal(areaTreeFull.district[current.id])) {
        isFull = true
      }
      if (isFull && current.id != 1 && current.level < 3) {
        // 这里加1的表示选中的全地址
        current.level += 1
      }
      selectAddrs.push({
        ...current,
        isFull,
      })
    }
  })
  return { selectAddrs, areaInfos }
}

/** 初始化地址数据
 * @param areas 已选中的地址数组
 */
export async function initAddress(areas: (string | number)[] = []): Promise<TTreeState> {
  /** 5576, 510104 */
  await setAreaTreeFull()
  let oneArea: TTreeFullVal = handlerData($.deepClone(areaTreeFull.province), 1)
  let twoArea: TTreeFullVal = []
  let threeArea: TTreeFullVal = []
  const { selectAddrs, areaInfos } = await getSelectSearches(areas)

  let areaInfo
  if ($.isArrayVal(areaInfos)) {
    [areaInfo] = areaInfos // 将数组第一个元素赋值给areaInfo
  }
  if (!areaInfo || !areaInfo.current || areaInfo.current.id == 1) {
    oneArea[0].checked = true
    const oneAreaId = oneArea[0].id
    twoArea = $.deepClone(areaTreeFull.city[oneAreaId]) || []
  } else {
    if (areaInfo.province) {
      /** 设置显示的二级地址 */
      twoArea = setSelect(areaTreeFull.city[areaInfo.province.id], areaInfo.city.id)
    }
    if (addrConf.level == 3 && areaInfo.city) {
      /** 设置显示的三级地址 */
      threeArea = setSelect(areaTreeFull.district[areaInfo.city.id], areaInfo.district.id)
    }

    const dataResult = selectHandler({
      oneArea,
      twoArea,
      threeArea,
      selectAddrs,
    })

    oneArea = dataResult.oneArea
    twoArea = dataResult.twoArea
    threeArea = dataResult.threeArea
  }

  return { oneArea, twoArea, threeArea, selectAddrs }
}

/**
 * 处理选中事件
 * @param areaInfo ITreeArea: { province: '', city: '', district: '', current: '' }
 * @param level 当前页面UI的地址级别
 * */
export function selectAddress(area: TTreeVal, selecting: TTreeVal[]): TSelectAddress {
  const oneArea: TTreeFullVal = handlerData($.deepClone(areaTreeFull.province) || [], 1)
  let twoArea: TTreeFullVal = []
  let threeArea: TTreeFullVal = []
  let { provinceId, cityId, districtId } = getAreaId(area)

  /** 选择的是否是末级地址 */
  let isEnd = area.id == 1 // 如果为全国的话，就为true
  switch (Number(area.level)) {
  case 1:
    twoArea = $.deepClone(areaTreeFull.city[area.id]) || []
    break
  case 2:
    twoArea = $.deepClone(areaTreeFull.city[provinceId]) || []
    if (addrConf.level == 3 && !area.isFull) {
      threeArea = $.deepClone(areaTreeFull.district[area.id]) || []
    }
    isEnd = true
    if (addrConf.level == 3) {
      isEnd = !$.isArrayVal(threeArea)
    }
    break
  case 3:
    twoArea = $.deepClone(areaTreeFull.city[provinceId]) || []
    threeArea = $.deepClone(areaTreeFull.district[cityId]) || []
    isEnd = true
    break
  }

  if (!isEnd
    && addrConf.level == 3
    && !$.isArrayVal(threeArea)
    && $.isArrayVal(twoArea)
    && $.isArrayVal(selecting)) {
    // 这里处理第三列没有数据的显示逻辑
    const selectData = selecting.find(item => {
      if (item.gid) {
        return twoArea.some((two) => two.id == item.pid)
      }
      if (item.isFull) {
        return twoArea.some((two) => two.id == item.id)
      }
      return false
    })
    if (selectData && selectData.pid) {
      if (selectData.gid) {
        provinceId = selectData.gid
        cityId = selectData.pid
        districtId = selectData.id
      } else {
        provinceId = selectData.pid || provinceId
        cityId = selectData.id
        districtId = selectData.id
      }
      threeArea = $.deepClone(areaTreeFull.district[cityId]) || []
    }
  }

  /** 设置选中情况 */
  oneArea.forEach(item => {
    item.checked = item.id == provinceId
  })

  if ($.isArrayVal(twoArea)) {
    twoArea.forEach(item => {
      item.checked = item.id == cityId
      if (area.isFull) { // 如果是isFull，则是当前地址的全地址
        item.checked = item.id == area.id
      }
    })
  }
  if ($.isArrayVal(threeArea)) {
    threeArea.forEach(item => {
      item.checked = item.id == districtId
      if (area.isFull) { // 如果是isFull，则是当前地址的全地址
        item.checked = item.id == area.id
      }
    })
  }

  const twoAreaNew = handlerData(twoArea, 2)
  const threeAreaNew = handlerData(threeArea, 3)

  return {
    oneArea,
    twoArea: twoAreaNew,
    threeArea: threeAreaNew,
    isEnd,
  }
}

/** 处理选中数组 */
export function selectArrHandler(dataList: TTreeVal[], data: TTreeVal) {
  const list = dataList.slice()
  // 如果选择的是全国
  if (data.id == 1) {
    return [{ ...data }]
  }

  // Check if the data already exists in the list based on `id`
  const index = list.findIndex(item => item.id == data.id)
  if (index != -1) {
    list.splice(index, 1)
    return list
  }
  list.push(data)

  return list.filter((item, index) => {
    if (index == list.length - 1) {
      return true
    }
    if (data.id == item.pid) {
      return false
    }
    if (data.id == item.gid) {
      return false
    }
    if (item.id == data.pid) {
      return false
    }
    /* if (item.id == data.gid) {
      return false
    } */
    return item.id != data.gid
  })
}

/** 处理地址列表选中逻辑-包括每个级别的数量
 * @param addrState 地址列表数据
 * @param area 当前点击的地址信息
 */
export function selectHandler(addrState: TTreeState): TAddrTreeData {
  const { oneArea, selectAddrs } = addrState
  const twoArea = handlerData(addrState.twoArea, 2)
  const threeArea = handlerData(addrState.threeArea, 3)
  const selectIds = selectAddrs.map(item => +item.id)

  if ($.isArrayVal(oneArea)) {
    if ($.isArrayVal(twoArea)) {
      const threePid = twoArea[0].isFull ? twoArea[0].id : twoArea[0].pid

      /** 省地址的处理 */
      oneArea.forEach(item => {
        item.checked = false
        if (threePid == item.id) {
          item.checked = true
        }
      })
    } else {
      oneArea[0].checked = true
    }
  }

  if ($.isArrayVal(threeArea)) {
    const threePid = threeArea[0].isFull ? threeArea[0].id : threeArea[0].pid
    twoArea.forEach(item => {
      item.checked = false
      if (threePid == item.id) {
        item.checked = true
      }
    })
    /** 区域地址的处理 */
    threeArea.forEach(item => {
      if (selectIds.includes(+item.id)) {
        item.checked = true
      } else {
        item.checked = false
      }
    })
  } else if ($.isArrayVal(twoArea)) {
    /** 没有三级地址列表有城市地址的处理 */
    twoArea.forEach(item => {
      item.checked = false
      /** 已选中的地址 */
      if (selectIds.includes(+item.id)) {
        item.checked = true
      }
    })
  }
  return { oneArea, twoArea, threeArea }
}

/** 清空所有已选的id */
export function selectClear<T extends TAddrTreeData>(addrState: T): T {
  let { oneArea, twoArea, threeArea } = addrState
  oneArea = $.deepClone(oneArea)
  twoArea = $.deepClone(twoArea)
  threeArea = $.deepClone(threeArea)

  oneArea.forEach(item => {
    item.checked = false
    if ($.isArrayVal(twoArea)) {
      const threePid = twoArea[0].isFull ? twoArea[0].id : twoArea[0].pid
      item.checked = item.id == threePid
    }
  })

  twoArea.forEach(item => {
    item.checked = false
    if (addrConf.level == 3 && $.isArrayVal(threeArea)) {
      const threePid = threeArea[0].isFull ? threeArea[0].id : threeArea[0].pid
      item.checked = item.id == threePid
    }
  })

  threeArea.forEach(item => {
    item.checked = false
  })

  return { oneArea, twoArea, threeArea } as T
}
