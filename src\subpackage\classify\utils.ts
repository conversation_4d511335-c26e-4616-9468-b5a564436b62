import Taro from '@tarojs/taro'

/** 获取搜索头部高度 */
export const getTopHeight = async (setTopHeadHeight) => {
  const query = Taro.createSelectorQuery()
  query.select('#top-head') // 获取组件的 UID
    .boundingClientRect()
    .exec((rect) => {
      console.log('top-head:', rect)

      if ($.isArrayVal(rect)) {
        const { height } = rect[0] || {}
        setTopHeadHeight(height || 0)
      }
    })
}

/** 获取搜索框高度 */
export const getScrollViewHeight = async (setSeachHeadHeight) => {
  const query = Taro.createSelectorQuery()
  query.select('#search-head') // 获取组件的 UID
    .boundingClientRect()
    .exec((rect) => {
      console.log('search-head:', rect)

      if ($.isArrayVal(rect)) {
        const { height } = rect[0]
        setSeachHeadHeight(height)
      }
    })
}
/** 获取底部我的高度 */
export const getBtmWodeHeight = async (setBtmWodeHeight) => {
  const query = Taro.createSelectorQuery()
  query.select('#fotter') // 获取组件的 UID
    .boundingClientRect()
    .exec((rect) => {
      console.log('fotter:', rect)
      if ($.isArrayVal(rect)) {
        const { height } = rect[0]
        setBtmWodeHeight(height)
      }
    })
}
/** 设置行业滚动位置 */
export const setIndScrollPositon = (params, fns) => {
  const { indId } = params || {}
  const { setIndScrollIntoView } = fns || {}
  setIndScrollIntoView(`ypClass_${indId}`)
  setTimeout(() => {
    setIndScrollIntoView('')
  }, 50)
}

/** 设置职位滚动位置 */
export const setOccScrollPositon = (params, fns) => {
  const { indId, occId } = params || {}
  const { setOccScrollIntoView } = fns || {}
  setOccScrollIntoView(`ypClass_${indId}_${occId}`)
  setTimeout(() => {
    setOccScrollIntoView('')
  }, 50)
}

/** 获取职位滚动位置 */
export const getOccScrollPosition = (params, fns) => {
  const { indId, indOccIdOne, occSltedArr, occOneIdTwo } = params || {}
  if (!indId) return
  const nOneOccList = indOccIdOne[indId] || []
  // 当前行业下选中的职位
  const indOfSletedOcc = occSltedArr.filter(occslted => occslted.indexOf(`${indId}_`) >= 0)
  const spOccId = nOneOccList.find(oneOccid => {
    if (indOfSletedOcc.findIndex(sltId => `${sltId}` === `${indId}_${oneOccid}`) >= 0) {
      return true
    }
    const idx = (occOneIdTwo[`${indId}_${oneOccid}`] || []).findIndex(twoId => indOfSletedOcc.findIndex(sltId => `${sltId}` === `${indId}_${twoId}`) >= 0)
    return idx >= 0
  })
  setOccScrollPositon({ indId, occId: spOccId || nOneOccList[0] }, fns)
}

// 底部我的选择点击删除
export const delWodeItem = (item, params, fns) => {
  const { occSltedArr, occSlted } = params || {}
  const { setOccSltedObj } = fns || {}
  const { id } = item
  const nOccSltedArr = (occSltedArr || []).filter(slted => {
    const sltedArr = slted.split('_')
    return `${sltedArr[1]}` !== `${id}`
  })
  const nOccSlted = { ...(occSlted || {}) }
  const kyArr = Object.keys(nOccSlted)
  kyArr.forEach(ky => {
    const kyArr = ky.split('_')
    if (`${kyArr[1]}` === `${id}`) {
      delete nOccSlted[ky]
    }
  })
  setOccSltedObj && setOccSltedObj({ occSltedArr: nOccSltedArr, occSlted: nOccSlted })
}

// 招聘类和订单类互斥的toast提示
export const mayToastSelected = (params) => {
  const { item, isZpSingle, sltedItemArr } = params || {}
  const { mode } = item || {}
  if (isZpSingle && mode && $.isArrayVal(sltedItemArr) && sltedItemArr.some(({ mode }) => `${mode}` !== `${item.mode}`)) {
    if (`${mode}` === '1') {
      $.msg('以下职位可以多选')
    }
    if (`${mode}` === '2') {
      $.msg('以下职位只能单选')
    }
  }
}

// 保存传入的末级职位
export const addOccSlted = (nOccSltedObj, occId, industryOccMap) => {
  if (industryOccMap[occId]) {
    nOccSltedObj.occSlted[occId] = occId
    nOccSltedObj.occSltedArr.push(occId)
  }
}
