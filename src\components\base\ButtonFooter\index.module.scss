.footerStripes {
  display: block;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

.footerFill {
  display: block;
  padding: 24px 32px;
  pointer-events: none;
  color: transparent;
  background-color: transparent;
  opacity: 0;
}

.footerBtn {
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 10;
  width: 100%;
  padding: 24px 32px;
  color: white;
  background-color: white;
}

.footerCont {
  font-size: 34px;
  color: #FFF;
  background: $primary-color;
  font-weight: bold;
  width: auto !important;
  margin: 0 !important;
  padding: 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  height: 96px;
  text-align: center;
  border-radius: 12px;
  box-sizing: border-box;
  border-color: transparent;
  &:active {
    opacity: 0.7;
  }
  &.default {
    color: $base-color;
    background: #FFF;
    border-color: #E9EDF3;
  }
  &.ghost {
    color: $primary-color;
    background: #FFF;
    border-color: $primary-color;
  }
  &.disabled {
    opacity: 0.5;
  }
  &.primary.disabled {
    background-color: #99d3ff;
    color: rgba(255, 255, 255, 0.95);
    pointer-events: none;
    opacity: 1;
  }
}
