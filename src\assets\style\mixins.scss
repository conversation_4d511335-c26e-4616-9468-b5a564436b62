@import './variables.scss';

/** 文字多行溢出 */
@mixin textrow($row: 2, $pre-wrap: false) {
  display: -webkit-box;
  overflow: hidden;
  word-break: break-all;
  line-clamp: $row;
  -webkit-line-clamp: $row;
  -webkit-box-orient: vertical;

  @if $pre-wrap {
    white-space: pre-wrap;
  }
}

/** 超出隐藏显示省略号 */
@mixin ellip($row: 1, $pre-wrap: false) {
  @if $row < 2 {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  } @else {
    @include textrow($row, $pre-wrap);
  }
}

/** 文字按照单个英文符号或者单个字母进行换行 */
@mixin break-all() {
  white-space: normal;
  word-break: break-all;
}

/** ios和安卓底部的小横条安全高度 */
@mixin safe-area($otherHeight: 0rpx) {
  padding-bottom: calc(#{$otherHeight} + constant(safe-area-inset-bottom));
  padding-bottom: calc(#{$otherHeight} + env(safe-area-inset-bottom));
}

/** 添加阴影 */
@mixin shadow($px: 10px) {
  box-shadow: 0 0 $px $shadow-color;
}

@mixin titleLine($width: 2, $height: 100%) {
  &::before {
    position: absolute;
    top: 0;
    left: 0;
    width: $width * 1px;
    height: $height;
    background: $primary-color;
    content: '';
  }
}

/** cdn图片背景 */
@mixin bg($img) {
  background-image: url('https://staticscdn.zgzpsjz.com/insurance-h5/images/#{$img}');
}

/** Mini Program cdn图片背景 */
@mixin miniProgramBg($img) {
  background-image: url('https://staticscdn.zgzpsjz.com/miniprogram/images/#{$img}');
}

/** 标题样式 */
@mixin title($fontSize: 32px) {
  color: $text-color;
  font-size: $fontSize;
  font-weight: bold;
}

/** 边框线样式 */
@mixin top-line($z-index: 1, $height: 1rpx, $primary-color: $line-color) {
  position: relative;

  &::before {
    content: ' ';
    display: block;
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    z-index: $z-index;
    height: $height;
    background-color: $primary-color;
  }
}
@mixin top-line-b($z-index: 1, $height: 1rpx, $primary-color: $line-color) {
  border-top: $height solid $primary-color;
}

@mixin right-line($z-index: 1, $height: 1rpx, $primary-color: $line-color) {
  position: relative;

  &::after {
    content: ' ';
    display: block;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: $z-index;
    width: $height;
    background-color: $primary-color;
  }
}
@mixin right-line-b($z-index: 1, $height: 1rpx, $primary-color: $line-color) {
  border-right: $height solid $primary-color;
}

@mixin bottom-line($z-index: 1, $height: 1rpx, $primary-color: $line-color) {
  position: relative;

  &::after {
    content: ' ';
    display: block;
    position: absolute;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: $z-index;
    height: $height;
    background-color: $primary-color;
  }
}
@mixin bottom-line-b($z-index: 1, $height: 1rpx, $primary-color: $line-color) {
  border-bottom: $height solid $primary-color;
}

@mixin left-line($z-index: 1, $height: 1rpx, $primary-color: $line-color) {
  position: relative;

  &::before {
    content: ' ';
    display: block;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: $z-index;
    width: $height;
    background-color: $primary-color;
  }
}
@mixin left-line-b($z-index: 1, $height: 1rpx, $primary-color: $line-color) {
  border-left: $height solid $primary-color;
}

/** Active 状态样式 */
@mixin active($opacity: 0.7) {
  &:active {
    opacity: $opacity;
  }
}

/** Hover 状态样式 */
@mixin hover($opacity: 0.7) {
  &:hover {
    opacity: $opacity;
  }
}


/** 去掉 button 默认样式 */
@mixin btn() {
  border: 0 !important;
  margin: 0 !important;
  width: auto !important;
  padding: 0;
  font-size: 28rpx;
  line-height: normal;
  background-color: transparent;
  color: rgba(0, 0, 0, 0.85);
  font-weight: normal;
}

/** 主按钮样式 */
@mixin btn-primary() {
  @include btn();
  display: flex;
  align-items: center;
  justify-content: center;
  color: #FFF;
  height: 96rpx;
  padding: 0rpx 24rpx;
  border-radius: 16rpx;
  background: $primary-color;
  font-size: 34rpx;
  font-weight: bold;
}

/** 隐藏样式 */
@mixin hidden($is-none: true) {
  @if $is-none {
    display: none !important;
  } @else {
    visibility: hidden !important;
    overflow: hidden !important;
    width: 0 !important;
    height: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    border: 0 !important;
    font-size: 0 !important;
    line-height: 0 !important;
    background: transparent !important;
    flex: none !important;
    pointer-events: none !important;
  }
}


/** 图标居中 */
@mixin icon-center() {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

/** 弹性布局 */
@mixin flex($direction: row) {
  display: flex;
  flex-direction: $direction;
  align-items: center;
  justify-content: center;
}

@mixin flexR() {
  display: flex;
  flex-direction: row;
}

@mixin flexRC() {
  @include flexR();
  align-items: center;
}

@mixin flexRCC() {
  @include flexRC();
  justify-content: center;
}

@mixin flexRCSB() {
  @include flexRC();
  justify-content: space-between;
}
