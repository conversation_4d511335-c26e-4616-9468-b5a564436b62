/*
 * @Date: 2024-11-23 10:18:38
 * @Description: 招工卡片
 */

import { memo } from 'react'
import cn from 'classnames'
import { RichText, View as V } from '@tarojs/components'
import s from './index.module.scss'
import { useSelector } from '@/core/store'
import useCardReport from '@/hooks/useCardExposure'
import { haversineDistance } from '@/utils/location/index'

/** 这几个需要展示为蓝色 */
const blueTag = [1, 10, 11, 12]

export default memo(({ info, onClickCard, index }: any) => {
  const userLocation = useSelector((state) => state.storage.userLocation)
  const distance = userLocation.success
    && info.location
    && info.location.latitude
    && info.location.longitude
    ? haversineDistance(
      userLocation.latitude,
      userLocation.longitude,
      info.location.latitude,
      info.location.longitude,
    )
    : null
  const onCardClick = () => {
    onClickCard.current(info)
  }

  const classId = `card-${index}-${info.jobId}`

  useCardReport(classId, () => [$.getElementTop('#listbody'), 0], async (exposureDuration) => {
    // console.log('卡片上报', info.title, Number(exposureDuration / 1000).toFixed(2))
    $.report.event('homeWorklistExposure', {
      info_id: String(info.jobId),
      search_result: '',
      keywords_source: '',
      exposure_duration: Number(exposureDuration / 1000).toFixed(2),
      check_degree: String(info.checkDegreeStatus),
      detailed_address: info.address,
      post_distance: distance || '',
      job_location: [info.location?.longitude, info.location?.latitude].join(','),
      position_status: String(info.isEnd.code),
      divisionline_area: '0',
      free_information: '免费',
      part_time_options: '',
      extInfo: '',
      feedback_exposure: '0',
      occupations_type: info.occMode == 1 ? '订单' : '招聘',
      ...info.buriedData,
    })
  })

  return (
    <V className={`${s.card} ${classId}`} onClick={() => onCardClick()}>
      <RichText className={s.title} nodes={info.title} />
      <V className={s.tags}>
        {info.showTags.map((item) => {
          return (
            <V
              className={cn(s.tag, blueTag.includes(item.type) && s.tagBlue)}
              key={item.name}
            >
              {item.name}
            </V>
          )
        })}
      </V>
      <V className={s.footer}>
        <V className={s.address}>{info.address}</V>
        <V className={s.footerInfo}>
          <V className={s.location}>{distance}</V>
          <V className={s.date}>{info.showDate}</V>
        </V>
      </V>
    </V>
  )
})
